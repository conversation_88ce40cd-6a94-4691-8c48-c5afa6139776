# Crypto Trading Signal System

An advanced cryptocurrency trading signal system that uses multiple technical indicators to identify high-probability trading opportunities.

## System Components

### 1. Data Collection (`make_Data - 4h - 2500.py`)
- Collects historical 4-hour candle data from Binance
- Downloads 2500 candles for each symbol
- Calculates technical indicators
- Handles API rate limiting and errors
- Uses multithreading for faster downloads

### 2. Signal Analysis (`combo_signal_weights.py`)
- Analyzes combinations of technical indicators
- Calculates success probabilities and expected returns
- Uses advanced statistical metrics (Sharpe ratio, etc.)
- Generates weighted scores for each combination
- Outputs results to `combo_signal_weights.json`

### 3. Strategy Selection (`top_combos_selector.py`)
- Filters combinations based on quality criteria
- Ranks combinations using a comprehensive scoring system
- Selects top 5 most reliable combinations
- Outputs results to `top_combos.json`

### 4. Real-time Monitoring (`crypto_monitor_advanced.py`)
- Monitors markets in real-time
- Applies selected indicator combinations
- Sends alerts when high-probability setups occur
- Includes cooldown periods to prevent alert spam
- Integrates with Telegram for notifications

## Setup Instructions

1. Install required Python packages:
```bash
pip install ccxt pandas numpy ta-lib python-telegram-bot python-dotenv
```

2. Create a `.env` file with your Telegram credentials:
```
TELEGRAM_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

3. Update `symbols.txt` with the cryptocurrency pairs you want to monitor.

4. Run the system in order:

```bash
# 1. Collect historical data
python "make_Data - 4h - 2500.py"

# 2. Analyze indicator combinations
python combo_signal_weights.py

# 3. Select best combinations
python top_combos_selector.py

# 4. Start monitoring
python crypto_monitor_advanced.py
```

## System Parameters

### Data Collection
- Timeframe: 4 hours
- History: 2500 candles
- Concurrent downloads: 5 symbols

### Signal Analysis
- Minimum success rate: 55%
- Minimum profit target: 3%
- Minimum sample size: 30 signals

### Monitoring
- Alert cooldown: 12 hours per symbol
- API retry attempts: 3
- Update interval: 5 minutes

## Technical Indicators Used

1. **RSI (Relative Strength Index)**
   - Identifies oversold conditions
   - Default threshold: 30

2. **MACD (Moving Average Convergence Divergence)**
   - Identifies trend changes
   - Uses bullish crossovers in negative territory

3. **OBV (On-Balance Volume)**
   - Confirms price movements with volume
   - Uses 20-period SMA for trend confirmation

4. **ADX (Average Directional Index)**
   - Measures trend strength
   - Minimum threshold: 25

5. **CMF (Chaikin Money Flow)**
   - Measures buying/selling pressure
   - Confirms volume-based trends

## Best Practices

1. **Risk Management**
   - Never trade solely based on technical signals
   - Use proper position sizing
   - Set stop-loss orders
   - Consider market conditions

2. **System Maintenance**
   - Regularly update historical data
   - Monitor signal quality metrics
   - Adjust parameters if needed
   - Keep dependencies updated

3. **Monitoring**
   - Check system logs regularly
   - Verify Telegram connectivity
   - Monitor API rate limits
   - Backup configuration files

## Error Handling

The system includes comprehensive error handling for:
- API connection issues
- Data validation
- Calculation errors
- Network timeouts
- File I/O operations

## Logging

All components maintain detailed logs:
- `data_collection.log`
- `signal_analysis.log`
- `combo_selector.log`
- `crypto_monitor.log`

## Contributing

Feel free to submit issues and enhancement requests!

import os
import pandas as pd
import numpy as np
import json
import logging
from itertools import combinations
from typing import Dict, List, Optional, Tuple, TypedDict
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('signal_analysis.log'),
        logging.StreamHandler()
    ]
)

# Type definitions
class SignalConfig(TypedDict):
    name: str
    description: str
    weight: float

class SignalStats(TypedDict):
    total_signals: int
    success_probability: float
    average_return: float
    sharpe_ratio: float
    volatility: float
    weight: float

# Constants
DATA_FOLDER = "data"
OUTPUT_FILE = "combo_signal_weights.json"
MIN_DATA_POINTS = 100
LOOKBACK_PERIOD = 3
MIN_PROFIT_THRESHOLD = 0.03
RISK_FREE_RATE = 0.02

# Technical Parameters
RSI_PERIOD = 14
RSI_OVERSOLD = 30
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9
ADX_PERIOD = 14
ADX_THRESHOLD = 25
OBV_SMA_PERIOD = 20

def get_signal_functions() -> Dict[str, SignalConfig]:
    """Return the configuration of all technical indicators"""
    return {
        'rsi': {
            'name': 'RSI',
            'description': 'Relative Strength Index',
            'weight': 1.2
        },
        'macd': {
            'name': 'MACD',
            'description': 'Moving Average Convergence Divergence',
            'weight': 1.1
        },
        'obv': {
            'name': 'OBV',
            'description': 'On-Balance Volume',
            'weight': 1.3
        },
        'adx': {
            'name': 'ADX',
            'description': 'Average Directional Index',
            'weight': 1.15
        },
        'cmf': {
            'name': 'CMF',
            'description': 'Chaikin Money Flow',
            'weight': 1.05
        }
    }

def calculate_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate all technical indicators"""
    try:
        # RSI
        price_diff = df['close'].diff()
        gains = price_diff.where(price_diff > 0, 0)
        losses = -price_diff.where(price_diff < 0, 0)
        avg_gains = gains.rolling(window=RSI_PERIOD).mean()
        avg_losses = losses.rolling(window=RSI_PERIOD).mean()
        rs = avg_gains / avg_losses
        df['rsi'] = 100 - (100 / (1 + rs))

        # MACD
        exp1 = df['close'].ewm(span=MACD_FAST, adjust=False).mean()
        exp2 = df['close'].ewm(span=MACD_SLOW, adjust=False).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=MACD_SIGNAL, adjust=False).mean()

        # OBV
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
        df['obv_sma'] = df['obv'].rolling(window=OBV_SMA_PERIOD).mean()

        # ADX
        high_diff = df['high'].diff()
        low_diff = df['low'].diff()
        
        tr = pd.DataFrame()
        tr['h-l'] = df['high'] - df['low']
        tr['h-pc'] = abs(df['high'] - df['close'].shift())
        tr['l-pc'] = abs(df['low'] - df['close'].shift())
        tr = tr.max(axis=1)
        
        pos_dir = pd.Series(np.where((high_diff > 0) & (high_diff > -low_diff), high_diff, 0))
        neg_dir = pd.Series(np.where((low_diff < 0) & (-low_diff > high_diff), -low_diff, 0))
        
        tr_avg = tr.rolling(ADX_PERIOD).mean()
        pos_di = 100 * (pos_dir.rolling(ADX_PERIOD).mean() / tr_avg)
        neg_di = 100 * (neg_dir.rolling(ADX_PERIOD).mean() / tr_avg)
        
        df['adx'] = 100 * abs(pos_di - neg_di) / (pos_di + neg_di)
        df['adx'] = df['adx'].rolling(ADX_PERIOD).mean()
        df['adx_pos'] = pos_di
        df['adx_neg'] = neg_di

        # CMF
        mfm = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
        mfv = mfm * df['volume']
        df['cmf'] = mfv.rolling(20).sum() / df['volume'].rolling(20).sum()

        return df.dropna()
        
    except Exception as e:
        logging.error(f"Error calculating indicators: {e}")
        return df

def check_signal_condition(df: pd.DataFrame, row_idx: int, indicator: str) -> bool:
    """Check if a specific indicator gives a signal at the given index"""
    try:
        if indicator == 'rsi':
            return df['rsi'].iloc[row_idx] < RSI_OVERSOLD
        elif indicator == 'macd':
            return (df['macd'].iloc[row_idx] > df['macd_signal'].iloc[row_idx] and 
                   df['macd'].iloc[row_idx] < 0)
        elif indicator == 'obv':
            return df['obv'].iloc[row_idx] > df['obv_sma'].iloc[row_idx]
        elif indicator == 'adx':
            return (df['adx'].iloc[row_idx] > ADX_THRESHOLD and 
                   df['adx_pos'].iloc[row_idx] > df['adx_neg'].iloc[row_idx])
        elif indicator == 'cmf':
            return df['cmf'].iloc[row_idx] > 0
        return False
    except Exception as e:
        logging.error(f"Error checking {indicator} signal: {e}")
        return False

def analyze_combination(df: pd.DataFrame, combo: Tuple[str, ...]) -> Optional[SignalStats]:
    """Analyze a specific combination of indicators"""
    try:
        signals = []
        combo_weight = np.prod([get_signal_functions()[ind]['weight'] for ind in combo])
        
        for i in range(len(df)):
            if all(check_signal_condition(df, i, ind) for ind in combo):
                signals.append(i)
        
        if not signals:
            return None
            
        # Calculate future returns
        future_returns = []
        for idx in signals:
            if idx + LOOKBACK_PERIOD < len(df):
                ret = (df['close'].iloc[idx + LOOKBACK_PERIOD] - df['close'].iloc[idx]) / df['close'].iloc[idx]
                future_returns.append(ret)
        
        if not future_returns:
            return None
            
        returns = pd.Series(future_returns)
        
        stats: SignalStats = {
            'total_signals': len(returns),
            'success_probability': (returns > MIN_PROFIT_THRESHOLD).mean(),
            'average_return': returns.mean(),
            'sharpe_ratio': (returns.mean() - RISK_FREE_RATE) / (returns.std() + 1e-10),
            'volatility': returns.std(),
            'weight': combo_weight
        }
        
        return stats
    
    except Exception as e:
        logging.error(f"Error analyzing combination {'+'.join(combo)}: {e}")
        return None

def main() -> None:
    """Main execution function"""
    try:
        logging.info("Starting signal combination analysis...")
        
        # Get all indicator combinations
        indicators = list(get_signal_functions().keys())
        all_combos: List[Tuple[str, ...]] = []
        for r in [2, 3]:
            all_combos.extend(list(combinations(indicators, r)))
            
        # Initialize results storage
        stats_by_combo: Dict[str, SignalStats] = {}
        
        # Process all files
        data_files = [f for f in os.listdir(DATA_FOLDER) if f.endswith('.csv')]
        total_files = len(data_files)
        
        for i, file_name in enumerate(data_files, 1):
            try:
                file_path = os.path.join(DATA_FOLDER, file_name)
                df = pd.read_csv(file_path)
                
                if len(df) < MIN_DATA_POINTS:
                    continue
                    
                df = calculate_indicators(df)
                
                # Analyze each combination
                for combo in all_combos:
                    stats = analyze_combination(df, combo)
                    if stats:
                        combo_key = '+'.join(combo)
                        if combo_key not in stats_by_combo:
                            stats_by_combo[combo_key] = stats
                        else:
                            # Update existing stats
                            old_stats = stats_by_combo[combo_key]
                            stats_by_combo[combo_key] = {
                                'total_signals': old_stats['total_signals'] + stats['total_signals'],
                                'success_probability': (old_stats['success_probability'] * old_stats['total_signals'] + 
                                                      stats['success_probability'] * stats['total_signals']) / 
                                                     (old_stats['total_signals'] + stats['total_signals']),
                                'average_return': (old_stats['average_return'] * old_stats['total_signals'] + 
                                                 stats['average_return'] * stats['total_signals']) / 
                                                (old_stats['total_signals'] + stats['total_signals']),
                                'sharpe_ratio': max(old_stats['sharpe_ratio'], stats['sharpe_ratio']),
                                'volatility': (old_stats['volatility'] * old_stats['total_signals'] + 
                                             stats['volatility'] * stats['total_signals']) / 
                                            (old_stats['total_signals'] + stats['total_signals']),
                                'weight': stats['weight']
                            }
                
                logging.info(f"Processed {i}/{total_files}: {file_name}")
                
            except Exception as e:
                logging.error(f"Error processing {file_name}: {e}")
                continue
        
        # Save results
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats_by_combo, f, indent=2)
            
        logging.info(f"Analysis complete. Results saved to {OUTPUT_FILE}")
        
    except Exception as e:
        logging.error(f"Critical error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()

# نظام إشارات تداول العملات المشفرة

نظام متقدم لتداول العملات المشفرة يستخدم مجموعة من المؤشرات الفنية لتحديد فرص التداول عالية الاحتمالية.

## مكونات النظام

### ١. جمع البيانات (`make_Data - 4h - 2500.py`)
- يجمع بيانات الشموع التاريخية كل 4 ساعات من Binance
- يقوم بتحميل 2500 شمعة لكل عملة
- يحسب المؤشرات الفنية
- يتعامل مع حدود معدل API
- يستخدم المعالجة متعددة المسارات للتحميل السريع

### ٢. تحليل الإشارات (`combo_signal_weights.py`)
- يحلل مجموعات المؤشرات الفنية
- يحس<PERSON> احتمالات النجاح والعوائد المتوقعة
- يستخدم مقاييس إحصائية متقدمة (نسبة شارب وغيرها)
- يولد درجات مرجحة لكل مجموعة
- يخرج النتائج إلى ملف `combo_signal_weights.json`

### ٣. اختيار الاستراتيجية (`top_combos_selector.py`)
- يصفي المجموعات بناءً على معايير الجودة
- يصنف المجموعات باستخدام نظام تقييم شامل
- يختار أفضل 5 مجموعات موثوقة
- يخرج النتائج إلى ملف `top_combos.json`

### ٤. المراقبة في الوقت الفعلي (`crypto_monitor_advanced.py`)
- يراقب الأسواق في الوقت الفعلي
- يطبق مجموعات المؤشرات المختارة
- يرسل تنبيهات عند اكتشاف إعدادات عالية الاحتمالية
- يتضمن فترات تهدئة لمنع تكرار التنبيهات
- يتكامل مع تيليجرام للإشعارات

## تعليمات الإعداد

١. تثبيت حزم بايثون المطلوبة:
```bash
pip install ccxt pandas numpy ta-lib python-telegram-bot python-dotenv
```

٢. إنشاء ملف `.env` مع بيانات اعتماد تيليجرام:
```
TELEGRAM_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

٣. تحديث ملف `symbols.txt` بأزواج العملات المشفرة التي تريد مراقبتها.

٤. تشغيل النظام بالترتيب:

```bash
# ١. جمع البيانات التاريخية
python "make_Data - 4h - 2500.py"

# ٢. تحليل مجموعات المؤشرات
python combo_signal_weights.py

# ٣. اختيار أفضل المجموعات
python top_combos_selector.py

# ٤. بدء المراقبة
python crypto_monitor_advanced.py
```

## معلمات النظام

### جمع البيانات
- الإطار الزمني: 4 ساعات
- التاريخ: 2500 شمعة
- التحميلات المتزامنة: 5 عملات

### تحليل الإشارات
- الحد الأدنى لمعدل النجاح: 55%
- الحد الأدنى للربح المستهدف: 3%
- الحد الأدنى لحجم العينة: 30 إشارة

### المراقبة
- فترة التهدئة للتنبيهات: 12 ساعة لكل عملة
- محاولات إعادة الاتصال بـ API: 3
- فترة التحديث: 5 دقائق

## المؤشرات الفنية المستخدمة

١. **مؤشر القوة النسبية (RSI)**
   - يحدد حالات التشبع في البيع
   - العتبة الافتراضية: 30

٢. **تقارب وتباعد المتوسطات المتحركة (MACD)**
   - يحدد تغيرات الاتجاه
   - يستخدم التقاطعات الصاعدة في المنطقة السلبية

٣. **حجم التوازن (OBV)**
   - يؤكد تحركات السعر مع الحجم
   - يستخدم المتوسط المتحرك البسيط لـ 20 فترة للتأكيد

٤. **مؤشر الاتجاه المتوسط (ADX)**
   - يقيس قوة الاتجاه
   - الحد الأدنى للعتبة: 25

٥. **تدفق المال لشايكن (CMF)**
   - يقيس ضغط البيع والشراء
   - يؤكد الاتجاهات القائمة على الحجم

## أفضل الممارسات

١. **إدارة المخاطر**
   - عدم التداول فقط بناءً على الإشارات الفنية
   - استخدام حجم مركز مناسب
   - تعيين أوامر وقف الخسارة
   - مراعاة ظروف السوق

٢. **صيانة النظام**
   - تحديث البيانات التاريخية بانتظام
   - مراقبة مقاييس جودة الإشارات
   - تعديل المعلمات عند الحاجة
   - تحديث التبعيات

٣. **المراقبة**
   - فحص سجلات النظام بانتظام
   - التحقق من اتصال تيليجرام
   - مراقبة حدود معدل API
   - نسخ ملفات التكوين احتياطياً

## معالجة الأخطاء

يتضمن النظام معالجة شاملة للأخطاء لـ:
- مشاكل اتصال API
- التحقق من صحة البيانات
- أخطاء الحساب
- انتهاء مهلة الشبكة
- عمليات الملفات

## التسجيل

تحتفظ جميع المكونات بسجلات مفصلة:
- `data_collection.log`
- `signal_analysis.log`
- `combo_selector.log`
- `crypto_monitor.log`

## المساهمة

لا تتردد في تقديم المشكلات وطلبات التحسين!

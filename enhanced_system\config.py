"""
Configuration Management for Enhanced Crypto Trading System
Centralized configuration for all system components
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str = "sqlite:///crypto_trading.db"
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class CacheConfig:
    """Cache configuration"""
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    default_ttl: int = 3600
    max_memory: str = "256mb"

@dataclass
class ExchangeConfig:
    """Exchange API configuration"""
    exchange_id: str = "binance"
    api_key: str = ""
    api_secret: str = ""
    sandbox: bool = True
    rate_limit: int = 1200  # requests per minute
    timeout: int = 30

@dataclass
class TradingConfig:
    """Trading parameters"""
    symbols: list = None
    timeframes: list = None
    max_positions: int = 5
    default_risk_percent: float = 0.02
    max_portfolio_risk: float = 0.10
    commission: float = 0.001
    slippage: float = 0.001
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "DOT/USDT", "LINK/USDT"]
        if self.timeframes is None:
            self.timeframes = ["1h", "4h", "1d", "1w"]

@dataclass
class MLConfig:
    """Machine Learning configuration"""
    models_enabled: list = None
    ensemble_weights: dict = None
    retrain_interval_hours: int = 24
    min_training_samples: int = 1000
    feature_selection: bool = True
    cross_validation_folds: int = 5
    
    def __post_init__(self):
        if self.models_enabled is None:
            self.models_enabled = ["lstm", "xgboost", "random_forest", "linear"]
        if self.ensemble_weights is None:
            self.ensemble_weights = {
                "lstm": 0.35,
                "xgboost": 0.30,
                "random_forest": 0.25,
                "linear": 0.10
            }

@dataclass
class AlertConfig:
    """Alert system configuration"""
    telegram_enabled: bool = False
    telegram_token: str = ""
    telegram_chat_id: str = ""
    email_enabled: bool = False
    email_smtp_server: str = ""
    email_smtp_port: int = 587
    email_username: str = ""
    email_password: str = ""
    email_to: str = ""
    webhook_urls: list = None
    min_confidence: float = 0.70
    cooldown_hours: int = 4
    max_alerts_per_day: int = 20
    
    def __post_init__(self):
        if self.webhook_urls is None:
            self.webhook_urls = []

@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_risk_per_trade: float = 0.02
    max_portfolio_risk: float = 0.10
    max_correlation_exposure: float = 0.30
    stop_loss_atr_multiplier: float = 2.0
    take_profit_ratio: float = 2.0
    max_consecutive_losses: int = 3
    position_sizing_method: str = "fixed_percent"  # "fixed_percent", "kelly", "volatility_adjusted"
    volatility_lookback: int = 20

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    initial_capital: float = 100000
    commission: float = 0.001
    slippage: float = 0.001
    start_date: str = "2023-01-01"
    end_date: str = "2023-12-31"
    benchmark_symbol: str = "BTC/USDT"

@dataclass
class MonitoringConfig:
    """Performance monitoring configuration"""
    enabled: bool = True
    update_interval_seconds: int = 60
    max_memory_usage: float = 85.0
    max_cpu_usage: float = 80.0
    max_response_time: float = 5.0
    min_cache_hit_rate: float = 0.70
    cleanup_interval_hours: int = 24
    retain_data_days: int = 365

class ConfigManager:
    """Configuration manager for the trading system"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = Path(config_file)
        self.config = self._load_default_config()
        
        # Load from file if exists
        if self.config_file.exists():
            self.load_from_file()
        else:
            # Create default config file
            self.save_to_file()
        
        # Load from environment variables
        self._load_from_env()
        
        logger.info(f"Configuration loaded from {self.config_file}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            "database": DatabaseConfig(),
            "cache": CacheConfig(),
            "exchange": ExchangeConfig(),
            "trading": TradingConfig(),
            "ml": MLConfig(),
            "alerts": AlertConfig(),
            "risk": RiskConfig(),
            "backtest": BacktestConfig(),
            "monitoring": MonitoringConfig()
        }
    
    def load_from_file(self) -> bool:
        """Load configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                file_config = json.load(f)
            
            # Update configuration with file values
            for section, values in file_config.items():
                if section in self.config:
                    if isinstance(self.config[section], dict):
                        self.config[section].update(values)
                    else:
                        # Update dataclass fields
                        for key, value in values.items():
                            if hasattr(self.config[section], key):
                                setattr(self.config[section], key, value)
            
            logger.info("Configuration loaded from file successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading configuration from file: {e}")
            return False
    
    def save_to_file(self) -> bool:
        """Save configuration to JSON file"""
        try:
            # Convert dataclasses to dictionaries
            config_dict = {}
            for section, config_obj in self.config.items():
                if hasattr(config_obj, '__dict__'):
                    config_dict[section] = asdict(config_obj)
                else:
                    config_dict[section] = config_obj
            
            with open(self.config_file, 'w') as f:
                json.dump(config_dict, f, indent=2, default=str)
            
            logger.info("Configuration saved to file successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration to file: {e}")
            return False
    
    def _load_from_env(self):
        """Load sensitive configuration from environment variables"""
        # Database
        if os.getenv('DATABASE_URL'):
            self.config['database'].url = os.getenv('DATABASE_URL')
        
        # Exchange API
        if os.getenv('EXCHANGE_API_KEY'):
            self.config['exchange'].api_key = os.getenv('EXCHANGE_API_KEY')
        if os.getenv('EXCHANGE_API_SECRET'):
            self.config['exchange'].api_secret = os.getenv('EXCHANGE_API_SECRET')
        
        # Redis
        if os.getenv('REDIS_HOST'):
            self.config['cache'].redis_host = os.getenv('REDIS_HOST')
        if os.getenv('REDIS_PORT'):
            self.config['cache'].redis_port = int(os.getenv('REDIS_PORT'))
        
        # Telegram
        if os.getenv('TELEGRAM_TOKEN'):
            self.config['alerts'].telegram_token = os.getenv('TELEGRAM_TOKEN')
            self.config['alerts'].telegram_enabled = True
        if os.getenv('TELEGRAM_CHAT_ID'):
            self.config['alerts'].telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        # Email
        if os.getenv('EMAIL_USERNAME'):
            self.config['alerts'].email_username = os.getenv('EMAIL_USERNAME')
            self.config['alerts'].email_enabled = True
        if os.getenv('EMAIL_PASSWORD'):
            self.config['alerts'].email_password = os.getenv('EMAIL_PASSWORD')
        if os.getenv('EMAIL_TO'):
            self.config['alerts'].email_to = os.getenv('EMAIL_TO')
        
        logger.info("Environment variables loaded")
    
    def get(self, section: str, key: str = None, default: Any = None) -> Any:
        """
        Get configuration value
        
        Args:
            section: Configuration section
            key: Configuration key (optional)
            default: Default value if not found
            
        Returns:
            Configuration value
        """
        try:
            if section not in self.config:
                return default
            
            if key is None:
                return self.config[section]
            
            config_obj = self.config[section]
            if hasattr(config_obj, key):
                return getattr(config_obj, key)
            elif isinstance(config_obj, dict) and key in config_obj:
                return config_obj[key]
            else:
                return default
                
        except Exception as e:
            logger.error(f"Error getting configuration {section}.{key}: {e}")
            return default
    
    def set(self, section: str, key: str, value: Any) -> bool:
        """
        Set configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to set
            
        Returns:
            Success status
        """
        try:
            if section not in self.config:
                logger.error(f"Configuration section '{section}' not found")
                return False
            
            config_obj = self.config[section]
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
                return True
            elif isinstance(config_obj, dict):
                config_obj[key] = value
                return True
            else:
                logger.error(f"Cannot set {section}.{key}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting configuration {section}.{key}: {e}")
            return False
    
    def validate(self) -> Dict[str, List[str]]:
        """
        Validate configuration
        
        Returns:
            Dictionary of validation errors by section
        """
        errors = {}
        
        # Validate database
        db_errors = []
        if not self.config['database'].url:
            db_errors.append("Database URL is required")
        if db_errors:
            errors['database'] = db_errors
        
        # Validate exchange
        exchange_errors = []
        if self.config['exchange'].api_key and not self.config['exchange'].api_secret:
            exchange_errors.append("API secret is required when API key is provided")
        if exchange_errors:
            errors['exchange'] = exchange_errors
        
        # Validate trading
        trading_errors = []
        if self.config['trading'].default_risk_percent <= 0 or self.config['trading'].default_risk_percent > 0.1:
            trading_errors.append("Default risk percent should be between 0 and 0.1")
        if self.config['trading'].max_portfolio_risk <= 0 or self.config['trading'].max_portfolio_risk > 1.0:
            trading_errors.append("Max portfolio risk should be between 0 and 1.0")
        if trading_errors:
            errors['trading'] = trading_errors
        
        # Validate alerts
        alert_errors = []
        if self.config['alerts'].telegram_enabled and not self.config['alerts'].telegram_token:
            alert_errors.append("Telegram token is required when Telegram alerts are enabled")
        if self.config['alerts'].email_enabled and not self.config['alerts'].email_username:
            alert_errors.append("Email username is required when email alerts are enabled")
        if alert_errors:
            errors['alerts'] = alert_errors
        
        # Validate risk
        risk_errors = []
        if self.config['risk'].max_risk_per_trade <= 0 or self.config['risk'].max_risk_per_trade > 0.1:
            risk_errors.append("Max risk per trade should be between 0 and 0.1")
        if self.config['risk'].take_profit_ratio <= 0:
            risk_errors.append("Take profit ratio should be positive")
        if risk_errors:
            errors['risk'] = risk_errors
        
        if errors:
            logger.warning(f"Configuration validation found {len(errors)} issues")
        else:
            logger.info("Configuration validation passed")
        
        return errors
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration"""
        return self.config['database']
    
    def get_cache_config(self) -> CacheConfig:
        """Get cache configuration"""
        return self.config['cache']
    
    def get_exchange_config(self) -> ExchangeConfig:
        """Get exchange configuration"""
        return self.config['exchange']
    
    def get_trading_config(self) -> TradingConfig:
        """Get trading configuration"""
        return self.config['trading']
    
    def get_ml_config(self) -> MLConfig:
        """Get ML configuration"""
        return self.config['ml']
    
    def get_alert_config(self) -> AlertConfig:
        """Get alert configuration"""
        return self.config['alerts']
    
    def get_risk_config(self) -> RiskConfig:
        """Get risk configuration"""
        return self.config['risk']
    
    def get_backtest_config(self) -> BacktestConfig:
        """Get backtest configuration"""
        return self.config['backtest']
    
    def get_monitoring_config(self) -> MonitoringConfig:
        """Get monitoring configuration"""
        return self.config['monitoring']

# Global configuration instance
config_manager = ConfigManager()

# Convenience functions
def get_config(section: str, key: str = None, default: Any = None) -> Any:
    """Get configuration value"""
    return config_manager.get(section, key, default)

def set_config(section: str, key: str, value: Any) -> bool:
    """Set configuration value"""
    return config_manager.set(section, key, value)

def save_config() -> bool:
    """Save configuration to file"""
    return config_manager.save_to_file()

def validate_config() -> Dict[str, List[str]]:
    """Validate configuration"""
    return config_manager.validate()

# Example usage
if __name__ == "__main__":
    # Initialize configuration
    config = ConfigManager("example_config.json")
    
    # Validate configuration
    errors = config.validate()
    if errors:
        print("Configuration errors found:")
        for section, section_errors in errors.items():
            print(f"  {section}:")
            for error in section_errors:
                print(f"    - {error}")
    else:
        print("Configuration is valid")
    
    # Get some configuration values
    print(f"Database URL: {config.get('database', 'url')}")
    print(f"Trading symbols: {config.get('trading', 'symbols')}")
    print(f"ML models enabled: {config.get('ml', 'models_enabled')}")
    
    # Set a configuration value
    config.set('trading', 'max_positions', 10)
    
    # Save configuration
    config.save_to_file()
    print("Configuration saved")

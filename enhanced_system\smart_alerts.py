"""
Smart Alert System for Enhanced Crypto Trading
Provides intelligent filtering, confidence analysis, and multi-channel notifications
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import json
import asyncio
import aiohttp
from telegram import Bot
from telegram.error import TelegramError
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertPriority(Enum):
    """Alert priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Alert types"""
    SIGNAL = "signal"
    RISK_WARNING = "risk_warning"
    MARKET_UPDATE = "market_update"
    SYSTEM_STATUS = "system_status"

@dataclass
class AlertCondition:
    """Alert condition definition"""
    name: str
    condition_type: str  # 'price', 'indicator', 'volume', 'ml_prediction'
    operator: str  # '>', '<', '>=', '<=', '==', 'crossover', 'crossunder'
    threshold: float
    timeframe: str
    enabled: bool = True

@dataclass
class Alert:
    """Alert object"""
    id: str
    symbol: str
    alert_type: AlertType
    priority: AlertPriority
    title: str
    message: str
    timestamp: datetime
    conditions_met: List[str]
    confidence_score: float
    risk_reward_ratio: float
    suggested_action: str
    expiry_time: Optional[datetime] = None
    sent_channels: List[str] = None

class SmartAlertSystem:
    """Smart alert system with intelligent filtering"""
    
    def __init__(self, config: Dict = None):
        """Initialize the smart alert system"""
        self.config = config or {}
        self.alert_history = []
        self.active_alerts = {}
        self.alert_conditions = {}
        self.performance_tracker = {}
        self.cooldown_periods = {}
        
        # Notification channels
        self.telegram_bot = None
        self.email_config = None
        self.webhook_urls = []
        
        # Initialize notification channels
        self._setup_notification_channels()
        
        # Default alert conditions
        self._setup_default_conditions()
    
    def add_alert_condition(self, symbol: str, condition: AlertCondition):
        """Add a new alert condition for a symbol"""
        if symbol not in self.alert_conditions:
            self.alert_conditions[symbol] = []
        
        self.alert_conditions[symbol].append(condition)
        logger.info(f"Added alert condition '{condition.name}' for {symbol}")
    
    def evaluate_conditions(self, symbol: str, market_data: Dict) -> List[Alert]:
        """
        Evaluate alert conditions for a symbol
        
        Args:
            symbol: Trading symbol
            market_data: Current market data and indicators
            
        Returns:
            List of triggered alerts
        """
        try:
            triggered_alerts = []
            
            if symbol not in self.alert_conditions:
                return triggered_alerts
            
            for condition in self.alert_conditions[symbol]:
                if not condition.enabled:
                    continue
                
                # Check if condition is met
                if self._check_condition(condition, market_data):
                    # Check cooldown
                    if self._is_in_cooldown(symbol, condition.name):
                        continue
                    
                    # Generate alert
                    alert = self._generate_alert(symbol, condition, market_data)
                    if alert and self._should_send_alert(alert):
                        triggered_alerts.append(alert)
                        self._update_cooldown(symbol, condition.name)
            
            return triggered_alerts
            
        except Exception as e:
            logger.error(f"Error evaluating conditions for {symbol}: {e}")
            return []
    
    def process_ml_prediction(self, symbol: str, prediction_data: Dict) -> Optional[Alert]:
        """
        Process ML prediction and generate alert if needed
        
        Args:
            symbol: Trading symbol
            prediction_data: ML prediction results
            
        Returns:
            Alert if conditions are met
        """
        try:
            confidence = prediction_data.get('confidence', 0.0)
            direction = prediction_data.get('direction', 'neutral')
            prediction_value = prediction_data.get('prediction', 0.0)
            
            # Only generate alerts for high-confidence predictions
            if confidence < 0.7:
                return None
            
            # Check if prediction is significant
            if abs(prediction_value) < 0.02:  # Less than 2% predicted move
                return None
            
            # Determine priority based on confidence and prediction magnitude
            if confidence > 0.9 and abs(prediction_value) > 0.05:
                priority = AlertPriority.CRITICAL
            elif confidence > 0.8 and abs(prediction_value) > 0.03:
                priority = AlertPriority.HIGH
            else:
                priority = AlertPriority.MEDIUM
            
            # Generate alert
            alert = Alert(
                id=f"ml_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                alert_type=AlertType.SIGNAL,
                priority=priority,
                title=f"ML Signal: {symbol} - {direction.upper()}",
                message=self._format_ml_alert_message(symbol, prediction_data),
                timestamp=datetime.now(),
                conditions_met=['ml_prediction'],
                confidence_score=confidence,
                risk_reward_ratio=self._calculate_risk_reward(prediction_data),
                suggested_action=self._get_suggested_action(direction, confidence),
                expiry_time=datetime.now() + timedelta(hours=4),
                sent_channels=[]
            )
            
            return alert
            
        except Exception as e:
            logger.error(f"Error processing ML prediction for {symbol}: {e}")
            return None
    
    async def send_alert(self, alert: Alert) -> bool:
        """
        Send alert through configured channels
        
        Args:
            alert: Alert to send
            
        Returns:
            True if sent successfully
        """
        try:
            success = False
            
            # Send via Telegram
            if self.telegram_bot:
                telegram_success = await self._send_telegram_alert(alert)
                if telegram_success:
                    alert.sent_channels.append('telegram')
                    success = True
            
            # Send via Email
            if self.email_config:
                email_success = await self._send_email_alert(alert)
                if email_success:
                    alert.sent_channels.append('email')
                    success = True
            
            # Send via Webhooks
            for webhook_url in self.webhook_urls:
                webhook_success = await self._send_webhook_alert(alert, webhook_url)
                if webhook_success:
                    alert.sent_channels.append('webhook')
                    success = True
            
            if success:
                self.alert_history.append(alert)
                self.active_alerts[alert.id] = alert
                logger.info(f"Alert sent successfully: {alert.title}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending alert: {e}")
            return False
    
    def update_alert_performance(self, alert_id: str, outcome: str, profit_loss: float):
        """
        Update alert performance tracking
        
        Args:
            alert_id: Alert ID
            outcome: 'success', 'failure', 'timeout'
            profit_loss: Actual profit/loss percentage
        """
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                
                performance_data = {
                    'alert_id': alert_id,
                    'symbol': alert.symbol,
                    'outcome': outcome,
                    'profit_loss': profit_loss,
                    'confidence_score': alert.confidence_score,
                    'predicted_direction': alert.suggested_action,
                    'timestamp': datetime.now()
                }
                
                symbol = alert.symbol
                if symbol not in self.performance_tracker:
                    self.performance_tracker[symbol] = []
                
                self.performance_tracker[symbol].append(performance_data)
                
                # Remove from active alerts
                del self.active_alerts[alert_id]
                
                logger.info(f"Updated performance for alert {alert_id}: {outcome}")
                
        except Exception as e:
            logger.error(f"Error updating alert performance: {e}")
    
    def get_performance_metrics(self, symbol: str = None) -> Dict:
        """Get performance metrics for alerts"""
        try:
            if symbol:
                data = self.performance_tracker.get(symbol, [])
            else:
                data = []
                for symbol_data in self.performance_tracker.values():
                    data.extend(symbol_data)
            
            if not data:
                return {'total_alerts': 0}
            
            total_alerts = len(data)
            successful_alerts = len([d for d in data if d['outcome'] == 'success'])
            failed_alerts = len([d for d in data if d['outcome'] == 'failure'])
            
            success_rate = successful_alerts / total_alerts if total_alerts > 0 else 0
            
            profits = [d['profit_loss'] for d in data if d['outcome'] == 'success']
            losses = [d['profit_loss'] for d in data if d['outcome'] == 'failure']
            
            avg_profit = np.mean(profits) if profits else 0
            avg_loss = np.mean(losses) if losses else 0
            
            metrics = {
                'total_alerts': total_alerts,
                'successful_alerts': successful_alerts,
                'failed_alerts': failed_alerts,
                'success_rate': success_rate,
                'avg_profit': avg_profit,
                'avg_loss': avg_loss,
                'profit_factor': abs(avg_profit / avg_loss) if avg_loss != 0 else 0
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def _check_condition(self, condition: AlertCondition, market_data: Dict) -> bool:
        """Check if a condition is met"""
        try:
            if condition.condition_type == 'price':
                current_price = market_data.get('close', 0)
                return self._evaluate_operator(current_price, condition.operator, condition.threshold)
            
            elif condition.condition_type == 'indicator':
                indicator_value = market_data.get(condition.name.lower(), 0)
                return self._evaluate_operator(indicator_value, condition.operator, condition.threshold)
            
            elif condition.condition_type == 'volume':
                current_volume = market_data.get('volume', 0)
                avg_volume = market_data.get('volume_avg', current_volume)
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
                return self._evaluate_operator(volume_ratio, condition.operator, condition.threshold)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking condition {condition.name}: {e}")
            return False
    
    def _evaluate_operator(self, value: float, operator: str, threshold: float) -> bool:
        """Evaluate operator condition"""
        if operator == '>':
            return value > threshold
        elif operator == '<':
            return value < threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return abs(value - threshold) < 0.001
        return False
    
    def _generate_alert(self, symbol: str, condition: AlertCondition, market_data: Dict) -> Alert:
        """Generate alert from triggered condition"""
        try:
            alert_id = f"{symbol}_{condition.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Determine priority based on condition type and market conditions
            priority = self._determine_priority(condition, market_data)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(condition, market_data)
            
            # Generate message
            message = self._format_condition_alert_message(symbol, condition, market_data)
            
            alert = Alert(
                id=alert_id,
                symbol=symbol,
                alert_type=AlertType.SIGNAL,
                priority=priority,
                title=f"{condition.name} Alert: {symbol}",
                message=message,
                timestamp=datetime.now(),
                conditions_met=[condition.name],
                confidence_score=confidence,
                risk_reward_ratio=2.0,  # Default
                suggested_action=self._get_condition_action(condition),
                expiry_time=datetime.now() + timedelta(hours=2),
                sent_channels=[]
            )
            
            return alert
            
        except Exception as e:
            logger.error(f"Error generating alert: {e}")
            return None
    
    def _should_send_alert(self, alert: Alert) -> bool:
        """Determine if alert should be sent based on filters"""
        try:
            # Check minimum confidence
            min_confidence = self.config.get('min_confidence', 0.6)
            if alert.confidence_score < min_confidence:
                return False
            
            # Check priority filters
            min_priority = self.config.get('min_priority', AlertPriority.MEDIUM)
            priority_order = [AlertPriority.LOW, AlertPriority.MEDIUM, AlertPriority.HIGH, AlertPriority.CRITICAL]
            
            if priority_order.index(alert.priority) < priority_order.index(min_priority):
                return False
            
            # Check market hours (if configured)
            if self.config.get('market_hours_only', False):
                current_hour = datetime.now().hour
                if current_hour < 6 or current_hour > 22:  # Outside typical trading hours
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking alert filters: {e}")
            return True
    
    def _setup_notification_channels(self):
        """Setup notification channels"""
        try:
            # Telegram setup
            telegram_token = self.config.get('telegram_token')
            if telegram_token:
                self.telegram_bot = Bot(token=telegram_token)
            
            # Email setup
            email_config = self.config.get('email')
            if email_config:
                self.email_config = email_config
            
            # Webhook setup
            self.webhook_urls = self.config.get('webhooks', [])
            
        except Exception as e:
            logger.error(f"Error setting up notification channels: {e}")
    
    def _setup_default_conditions(self):
        """Setup default alert conditions"""
        # RSI conditions
        rsi_oversold = AlertCondition(
            name="RSI_Oversold",
            condition_type="indicator",
            operator="<",
            threshold=30,
            timeframe="4h"
        )
        
        rsi_overbought = AlertCondition(
            name="RSI_Overbought",
            condition_type="indicator",
            operator=">",
            threshold=70,
            timeframe="4h"
        )
        
        # Volume spike condition
        volume_spike = AlertCondition(
            name="Volume_Spike",
            condition_type="volume",
            operator=">",
            threshold=2.0,  # 2x average volume
            timeframe="4h"
        )
        
        # Store default conditions
        self.default_conditions = [rsi_oversold, rsi_overbought, volume_spike]
    
    def _is_in_cooldown(self, symbol: str, condition_name: str) -> bool:
        """Check if condition is in cooldown period"""
        key = f"{symbol}_{condition_name}"
        if key in self.cooldown_periods:
            return datetime.now() < self.cooldown_periods[key]
        return False
    
    def _update_cooldown(self, symbol: str, condition_name: str):
        """Update cooldown period for condition"""
        key = f"{symbol}_{condition_name}"
        cooldown_hours = self.config.get('cooldown_hours', 4)
        self.cooldown_periods[key] = datetime.now() + timedelta(hours=cooldown_hours)
    
    async def _send_telegram_alert(self, alert: Alert) -> bool:
        """Send alert via Telegram"""
        try:
            chat_id = self.config.get('telegram_chat_id')
            if not chat_id or not self.telegram_bot:
                return False
            
            message = self._format_telegram_message(alert)
            await self.telegram_bot.send_message(chat_id=chat_id, text=message, parse_mode='Markdown')
            return True
            
        except TelegramError as e:
            logger.error(f"Telegram error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error sending Telegram alert: {e}")
            return False
    
    async def _send_email_alert(self, alert: Alert) -> bool:
        """Send alert via email"""
        try:
            if not self.email_config:
                return False

            smtp_server = self.email_config.get('smtp_server')
            smtp_port = self.email_config.get('smtp_port', 587)
            username = self.email_config.get('username')
            password = self.email_config.get('password')
            to_email = self.email_config.get('to_email')

            if not all([smtp_server, username, password, to_email]):
                return False

            # Create message
            msg = MimeMultipart()
            msg['From'] = username
            msg['To'] = to_email
            msg['Subject'] = f"Trading Alert: {alert.title}"

            body = self._format_email_message(alert)
            msg.attach(MimeText(body, 'plain'))

            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            text = msg.as_string()
            server.sendmail(username, to_email, text)
            server.quit()

            logger.info(f"Email alert sent: {alert.title}")
            return True

        except Exception as e:
            logger.error(f"Error sending email alert: {e}")
            return False

    def _format_email_message(self, alert: Alert) -> str:
        """Format alert message for email"""
        message = f"Trading Alert: {alert.title}\n\n"
        message += f"Symbol: {alert.symbol}\n"
        message += f"Priority: {alert.priority.value.upper()}\n"
        message += f"Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"Confidence: {alert.confidence_score:.1%}\n"
        message += f"Suggested Action: {alert.suggested_action}\n\n"
        message += f"Details:\n{alert.message}\n\n"
        message += f"Risk/Reward Ratio: {alert.risk_reward_ratio:.1f}:1\n"

        if alert.expiry_time:
            message += f"Alert expires: {alert.expiry_time.strftime('%Y-%m-%d %H:%M:%S')}\n"

        return message
    
    async def _send_webhook_alert(self, alert: Alert, webhook_url: str) -> bool:
        """Send alert via webhook"""
        try:
            alert_data = asdict(alert)
            alert_data['timestamp'] = alert.timestamp.isoformat()
            if alert.expiry_time:
                alert_data['expiry_time'] = alert.expiry_time.isoformat()
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=alert_data) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Error sending webhook alert: {e}")
            return False
    
    def _format_telegram_message(self, alert: Alert) -> str:
        """Format alert message for Telegram"""
        emoji_map = {
            AlertPriority.LOW: "🔵",
            AlertPriority.MEDIUM: "🟡",
            AlertPriority.HIGH: "🟠",
            AlertPriority.CRITICAL: "🔴"
        }
        
        emoji = emoji_map.get(alert.priority, "⚪")
        
        message = f"{emoji} *{alert.title}*\n\n"
        message += f"📊 Symbol: `{alert.symbol}`\n"
        message += f"⏰ Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"🎯 Confidence: {alert.confidence_score:.1%}\n"
        message += f"💡 Action: {alert.suggested_action}\n\n"
        message += f"📝 {alert.message}"
        
        return message
    
    def _format_ml_alert_message(self, symbol: str, prediction_data: Dict) -> str:
        """Format ML prediction alert message"""
        direction = prediction_data.get('direction', 'neutral')
        confidence = prediction_data.get('confidence', 0.0)
        prediction = prediction_data.get('prediction', 0.0)
        
        message = f"ML models predict {direction} movement for {symbol}\n"
        message += f"Expected change: {prediction:.2%}\n"
        message += f"Model confidence: {confidence:.1%}"
        
        return message
    
    def _format_condition_alert_message(self, symbol: str, condition: AlertCondition, market_data: Dict) -> str:
        """Format condition alert message"""
        current_value = market_data.get(condition.name.lower(), 0)
        
        message = f"{condition.name} condition triggered for {symbol}\n"
        message += f"Current value: {current_value:.2f}\n"
        message += f"Threshold: {condition.threshold:.2f}\n"
        message += f"Timeframe: {condition.timeframe}"
        
        return message
    
    def _determine_priority(self, condition: AlertCondition, market_data: Dict) -> AlertPriority:
        """Determine alert priority"""
        # Simple priority logic - can be enhanced
        if 'critical' in condition.name.lower():
            return AlertPriority.CRITICAL
        elif 'high' in condition.name.lower():
            return AlertPriority.HIGH
        else:
            return AlertPriority.MEDIUM
    
    def _calculate_confidence(self, condition: AlertCondition, market_data: Dict) -> float:
        """Calculate confidence score for condition"""
        # Simple confidence calculation - can be enhanced
        base_confidence = 0.7
        
        # Add volume confirmation
        volume_ratio = market_data.get('volume', 1) / market_data.get('volume_avg', 1)
        if volume_ratio > 1.5:
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _calculate_risk_reward(self, prediction_data: Dict) -> float:
        """Calculate risk-reward ratio"""
        # Simple calculation - can be enhanced
        prediction = abs(prediction_data.get('prediction', 0.02))
        return min(prediction * 50, 3.0)  # Cap at 3:1
    
    def _get_suggested_action(self, direction: str, confidence: float) -> str:
        """Get suggested action based on direction and confidence"""
        if confidence > 0.8:
            if direction == 'up':
                return "Strong Buy"
            elif direction == 'down':
                return "Strong Sell"
        elif confidence > 0.6:
            if direction == 'up':
                return "Buy"
            elif direction == 'down':
                return "Sell"
        
        return "Monitor"
    
    def _get_condition_action(self, condition: AlertCondition) -> str:
        """Get suggested action for condition"""
        if 'oversold' in condition.name.lower():
            return "Consider Buy"
        elif 'overbought' in condition.name.lower():
            return "Consider Sell"
        elif 'volume' in condition.name.lower():
            return "Monitor for Breakout"
        else:
            return "Monitor"

# Example usage
if __name__ == "__main__":
    config = {
        'telegram_token': 'your_bot_token',
        'telegram_chat_id': 'your_chat_id',
        'min_confidence': 0.7,
        'cooldown_hours': 4
    }
    
    alert_system = SmartAlertSystem(config)
    
    # Add custom condition
    custom_condition = AlertCondition(
        name="Price_Breakout",
        condition_type="price",
        operator=">",
        threshold=50000,
        timeframe="4h"
    )
    
    alert_system.add_alert_condition("BTC/USDT", custom_condition)
    
    # Simulate market data
    market_data = {
        'close': 51000,
        'volume': 1000000,
        'volume_avg': 500000,
        'rsi': 25
    }
    
    # Evaluate conditions
    alerts = alert_system.evaluate_conditions("BTC/USDT", market_data)
    print(f"Generated {len(alerts)} alerts")

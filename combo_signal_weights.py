import os
import pandas as pd
import numpy as np
import json
import logging
from itertools import combinations
from typing import Dict, List, Optional, Tuple, TypedDict, Union, cast
from pathlib import Path
from ta.momentum import RSIIndicator
from ta.trend import MACD, ADXIndicator
from ta.volume import OnBalanceVolumeIndicator, ChaikinMoneyFlowIndicator

# Constants for signal conditions
rsi_oversold: float = 30.0
macd_signal_threshold: float = 0.0
adx_strong_trend: float = 25.0
cmf_threshold: float = 0.0
lookback_period: int = 14  # Default lookback period for calculations

class SignalConfig(TypedDict):
    name: str
    description: str
    weight: float

def evaluate_signal_conditions(df: pd.DataFrame, signal_name: str) -> bool:
    """Evaluate technical signal conditions for a given signal name"""
    try:
        if signal_name == 'rsi':
            return bool(df['rsi'].iloc[-1] < rsi_oversold)
        elif signal_name == 'macd':
            return bool((df['macd'].iloc[-1] > df['macd_signal'].iloc[-1]) and 
                       (df['macd'].iloc[-1] < macd_signal_threshold))
        elif signal_name == 'obv':
            obv_diff = cast(float, df['obv'].diff(5).iloc[-1])
            return bool(obv_diff > 0)
        elif signal_name == 'adx':
            return bool((df['adx'].iloc[-1] > adx_strong_trend) and 
                       (df['adx_pos'].iloc[-1] > df['adx_neg'].iloc[-1]))
        elif signal_name == 'cmf':
            return bool(df['cmf'].iloc[-1] > cmf_threshold)
        return False
    except (KeyError, IndexError) as e:
        logging.error(f"Error evaluating {signal_name} signal: {str(e)}")
        return False

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('signal_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Type hints
from typing import Dict, List, Optional, Union, TypedDict

class SignalConfig(TypedDict):
    name: str
    description: str
    weight: float

class SignalStats(TypedDict):
    total_signals: int
    success_probability: float
    average_return: float
    sharpe_ratio: float
    volatility: float
    weight: float

# Constants
data_folder: str = "data"
output_file: str = "combo_signal_weights.json"
min_data_points: int = 100
lookback_period: int = 3  # Candles to look ahead for profit calculation
min_profit_threshold: float = 0.03  # 3% minimum profit target
risk_free_rate: float = 0.02  # 2% risk-free rate for Sharpe calculation

# Technical Indicators Configuration
rsi_oversold: float = 30.0
rsi_overbought: float = 70.0
adx_strong_trend: float = 25.0
cmf_threshold: float = 0.0

def load_and_prepare_data(file_path: Union[str, Path]) -> Optional[pd.DataFrame]:
    """Load and prepare data with error handling"""
    try:
        df = pd.read_csv(file_path)
        if len(df) < min_data_points:
            logging.warning(f"Insufficient data points in {file_path}")
            return None
            
        # Convert timestamp and set as index
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.sort_values('timestamp', inplace=True)
        df.set_index('timestamp', inplace=True)
        
        # Calculate technical indicators
        rsi = RSIIndicator(close=df['close'], window=14)
        df['rsi'] = rsi.rsi()
        
        macd = MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        obv = OnBalanceVolumeIndicator(close=df['close'], volume=df['volume'])
        df['obv'] = obv.on_balance_volume()
        
        adx = ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        df['adx_pos'] = adx.adx_pos()
        df['adx_neg'] = adx.adx_neg()
        
        cmf = ChaikinMoneyFlowIndicator(high=df['high'], low=df['low'], 
                                      close=df['close'], volume=df['volume'])
        df['cmf'] = cmf.chaikin_money_flow()
        
        return df.dropna()
    except Exception as e:
        logging.error(f"Error loading/preparing {file_path}: {str(e)}")
        return None

# Advanced Technical Signal Definitions
def get_signal_functions() -> Dict[str, SignalConfig]:
    """Return signal definitions for indicators"""
    return {
        'rsi': {
            'name': 'RSI',
            'description': 'RSI oversold',
            'weight': 1.2
        },
        'macd': {
            'name': 'MACD',
            'description': 'MACD bullish crossover in negative territory',
            'weight': 1.1
        },
        'obv': {
            'name': 'OBV',
            'description': 'OBV rising trend',
            'weight': 1.3
        },
        'adx': {
            'name': 'ADX',
            'description': 'Strong bullish trend',
            'weight': 1.15
        },
        'cmf': {
            'name': 'CMF',
            'description': 'Positive money flow',
            'weight': 1.05
        }
    }

def calculate_future_returns(df: pd.DataFrame, 
                           signal_idx: pd.Index, 
                           lookback: int = lookback_period) -> pd.Series:
    """Calculate future returns for signal points"""
    future_prices = df['close'].shift(-lookback)
    returns = (future_prices - df['close']) / df['close']
    return returns[signal_idx]

def calculate_signal_stats(returns: pd.Series) -> Optional[Dict[str, float]]:
    """Calculate comprehensive signal statistics"""
    if len(returns) == 0:
        return None
        
    try:
        return {
            'total_signals': float(len(returns)),
            'success_probability': float((returns > min_profit_threshold).mean()),
            'average_return': float(returns.mean()),
            'sharpe_ratio': float((returns.mean() - risk_free_rate) / (returns.std() + 1e-6)),
            'max_return': float(returns.max()),
            'min_return': float(returns.min()),
            'std_dev': float(returns.std())
        }
    except Exception as e:
        logging.error(f"Error calculating signal stats: {str(e)}")
        return None
        'min_return': returns.min(),
        'volatility': returns.std()
    }
    
    return stats

# Type aliases and storage initialization
SignalCombination = Tuple[str, ...]
CombinationStats = Dict[str, float]
Returns = List[float]

signal_functions: Dict[str, SignalConfig] = get_signal_functions()
combos_stats: Dict[str, CombinationStats] = {}
returns_all: Dict[str, Returns] = {}

# Generate all possible 2 and 3 indicator combinations
all_combos: List[SignalCombination] = []
for r in [2, 3]:
    all_combos.extend(list(combinations(signal_functions.keys(), r)))

def get_signal_conditions(df: pd.DataFrame) -> Dict[str, Dict[str, Union[bool, float]]]:
    """Get signal conditions for each indicator"""
    conditions = {}
    for signal_name, config in signal_functions.items():
        conditions[signal_name] = {
            'condition': evaluate_signal_conditions(df, signal_name),
            'weight': config['weight']
        }
    return conditions

def analyze_all_symbols() -> Dict[str, Returns]:
    """Analyze all symbols and calculate combo statistics"""
    data_dir = Path(data_folder)
    if not data_dir.exists():
        logging.error(f"Data directory {data_folder} does not exist")
        return {}
        
    csv_files = list(data_dir.glob("*.csv"))
    total_files = len(csv_files)
    processed_files = 0
    
    returns_all: Dict[str, Returns] = {}
    
    for file_path in csv_files:
        symbol = file_path.stem  # Gets filename without extension
        
        # Load and process data
        df = load_and_prepare_data(file_path)
        if df is None:
            continue
            
        # Get signal conditions for this symbol
        signal_conditions = get_signal_conditions(df)
        
        # Analyze each combination
        for combo in all_combos:
            combo_key = '+'.join(combo)
            
            # Combine signals
            combo_signal = pd.Series(True, index=df.index)
            combo_weight = 1.0
            
            for indicator in combo:
                sig_condition = signal_conditions[indicator]
                combo_signal &= sig_condition['condition']
                combo_weight *= sig_condition['weight']
            
            # Get signal indices
            signal_idx = df.index[combo_signal]
            
            # Calculate returns for signals
            if len(signal_idx) > 0:
                returns = calculate_future_returns(df, signal_idx)
                
                # Store returns for later analysis
                if combo_key not in returns_all:
                    returns_all[combo_key] = []
                returns_all[combo_key].extend(returns.dropna().tolist())
        
        processed_files += 1
        logging.info(f"Processed {symbol}: {processed_files}/{total_files} ({processed_files/total_files*100:.1f}%)")
        
    return returns_all
        
        # Load and process data
        df = load_and_prepare_data(file_path)
        if df is None:
            continue
            
        # Get signal conditions for this symbol
        signal_conditions = get_signal_conditions(df)
        
        # Analyze each combination
        for combo in all_combos:
            combo_key = '+'.join(combo)
            
            # Combine signals
            combo_signal = pd.Series(True, index=df.index)
            combo_weight = 1.0
            
            for indicator in combo:
                condition = signal_conditions[indicator]
                combo_signal &= condition['condition']
                combo_weight *= condition['weight']
            
            # Get signal indices
            signal_idx = combo_signal[combo_signal].index
            
            # Calculate returns for signals
            if len(signal_idx) > 0:
                returns = calculate_future_returns(df, signal_idx)
                
                # Store returns for later analysis
                if combo_key not in returns_all:
                    returns_all[combo_key] = []
                returns_all[combo_key].extend(returns.dropna().tolist())
        
        processed_files += 1
        logging.info(f"Processed {symbol}: {processed_files}/{total_files} ({processed_files/total_files*100:.1f}%)")
        
    return returns_all

def calculate_final_weights() -> None:
    """Calculate final weights for all combinations"""
    for combo_key, returns_list in returns_all.items():
        if not returns_list:  # Skip if empty
            continue
            
        returns = pd.Series(returns_list)
        stats = calculate_signal_stats(returns)
        
        if stats is None:
            continue
            
        # Calculate composite weight based on multiple factors
        try:
            weight = (
                stats['success_probability'] * 0.4 +  # Success rate importance
                stats['average_return'] * 0.3 +      # Return importance
                (stats['max_return'] - stats['min_return']) * 0.2 +  # Range importance
                (1 / (stats['std_dev'] + 1e-6)) * 0.1  # Lower volatility preference
            )
            
            stats['weight'] = float(weight)
            combos_stats[combo_key] = stats
        except (KeyError, TypeError) as e:
            logging.error(f"Error calculating weights for {combo_key}: {str(e)}")
            continue

def normalize_weights() -> None:
    """Normalize weights to sum to 1.0"""
    total_weight = sum(stats['weight'] for stats in combos_stats.values())
    if total_weight > 0:
        for key in combos_stats:
            combos_stats[key]['weight'] /= total_weight

def main() -> None:
    """Main execution function"""
    try:
        logging.info("Starting signal combination analysis...")
        
        # Analyze all symbols
        analyze_all_symbols()
        
        # Calculate final weights
        calculate_final_weights()
        
        # Normalize weights
        normalize_weights()
        
        # Save results
        output_path = Path(output_file)
        with output_path.open('w', encoding='utf-8') as f:
            json.dump(combos_stats, f, indent=2, ensure_ascii=False)
            
        logging.info(f"Analysis complete. Results saved to {output_file}")
        
    except Exception as e:
        logging.error(f"Error in main execution: {str(e)}")
        raise

if __name__ == "__main__":
    main()

print(f"📊 تم تحليل {len(os.listdir(DATA_FOLDER))} عملة وحساب أوزان {len(combos_stats)} توليفة من المؤشرات.")

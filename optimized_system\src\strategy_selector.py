import json
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List
from datetime import datetime
from rich.console import Console
from rich.table import Table

# Load configuration
with open('config/settings.json', 'r') as f:
    CONFIG = json.load(f)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/strategy_selector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
console = Console()

class StrategySelector:
    def __init__(self):
        """Initialize the strategy selector"""
        self.analysis_file = 'config/signal_analysis.json'
        self.output_file = 'config/top_strategies.json'
        self.min_success_rate = CONFIG['signal_analysis']['min_success_rate']
        self.min_profit_target = CONFIG['signal_analysis']['min_profit_target']

    def calculate_sharpe_ratio(
        self,
        returns: List[float],
        risk_free_rate: float = 0.02
    ) -> float:
        """Calculate the Sharpe ratio for a set of returns"""
        if not returns:
            return 0
        
        returns = np.array(returns)
        excess_returns = returns - risk_free_rate / 365
        if len(excess_returns) < 2:
            return 0
            
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(365)

    def rank_strategies(self, combinations: List[Dict]) -> List[Dict]:
        """Rank strategies based on multiple criteria"""
        if not combinations:
            logger.warning("No combinations to rank")
            return []

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(combinations)
        
        # Calculate composite score components
        df['success_score'] = (df['success_rate'] - self.min_success_rate) / (1 - self.min_success_rate)
        df['return_score'] = df['average_return'] / df['average_return'].max()
        df['holding_score'] = 1 - (df['average_holding_period'] / df['average_holding_period'].max())
        
        # Calculate complexity penalty (more indicators = more complex)
        df['complexity'] = df['indicators'].apply(len)
        df['complexity_score'] = 1 - (df['complexity'] - df['complexity'].min()) / (df['complexity'].max() - df['complexity'].min())
        
        # Calculate composite score
        df['composite_score'] = (
            df['success_score'] * 0.4 +
            df['return_score'] * 0.3 +
            df['holding_score'] * 0.15 +
            df['complexity_score'] * 0.15
        )
        
        # Sort by composite score
        df = df.sort_values('composite_score', ascending=False)
        
        # Convert back to list of dictionaries
        ranked_strategies = df.to_dict('records')
        
        return ranked_strategies

    def select_top_strategies(self) -> List[Dict]:
        """Select the top performing strategies"""
        try:
            # Load analysis results
            with open(self.analysis_file, 'r') as f:
                analysis = json.load(f)
            
            if not analysis['combinations']:
                logger.error("No combinations found in analysis file")
                return []
            
            # Rank strategies
            ranked_strategies = self.rank_strategies(analysis['combinations'])
            
            # Select top 5 strategies
            top_strategies = ranked_strategies[:5]
            
            # Add metadata
            result = {
                'selection_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'parameters': {
                    'min_success_rate': self.min_success_rate,
                    'min_profit_target': self.min_profit_target
                },
                'strategies': top_strategies
            }
            
            # Save results
            with open(self.output_file, 'w') as f:
                json.dump(result, f, indent=4)
            
            # Display results
            self.display_results(top_strategies)
            
            return top_strategies
            
        except Exception as e:
            logger.error(f"Error selecting strategies: {e}")
            return []

    def display_results(self, strategies: List[Dict]) -> None:
        """Display the results in a formatted table"""
        table = Table(title="Top Trading Strategies")
        
        table.add_column("Rank", justify="right", style="cyan")
        table.add_column("Success Rate", justify="right", style="green")
        table.add_column("Avg Return", justify="right", style="green")
        table.add_column("Holding Period", justify="right", style="blue")
        table.add_column("Indicators", style="magenta")
        table.add_column("Score", justify="right", style="yellow")
        
        for i, strategy in enumerate(strategies, 1):
            table.add_row(
                f"#{i}",
                f"{strategy['success_rate']:.1%}",
                f"{strategy['average_return']:.1%}",
                f"{strategy['average_holding_period']:.1f}h",
                ", ".join(strategy['indicators'].keys()),
                f"{strategy['composite_score']:.3f}"
            )
        
        console.print(table)

def main():
    """Main entry point"""
    try:
        selector = StrategySelector()
        selector.select_top_strategies()
    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    main()

"""
Enhanced Crypto Trading System - Main Integration Module
Integrates all components into a unified trading system
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import threading
import time

# Import all enhanced modules
from config import ConfigManager
from database_manager import DatabaseManager
from risk_management import AdvancedRiskManager, RiskLevel
from advanced_indicators import AdvancedIndicators
from multi_timeframe_analysis import MultiTimeframeAnalyzer
from ml_ensemble import MLEnsemble
from smart_alerts import SmartAlertSystem
from advanced_backtesting import AdvancedBacktester
from performance_monitor import PerformanceMonitor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_trading_system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnhancedTradingSystem:
    """Main enhanced trading system"""
    
    def __init__(self, config_file: str = "config.json"):
        """Initialize the enhanced trading system"""
        logger.info("Initializing Enhanced Crypto Trading System...")
        
        # Load configuration
        self.config = ConfigManager(config_file)
        
        # Validate configuration
        errors = self.config.validate()
        if errors:
            logger.error("Configuration validation failed:")
            for section, section_errors in errors.items():
                for error in section_errors:
                    logger.error(f"  {section}: {error}")
            raise ValueError("Invalid configuration")
        
        # Initialize components
        self._initialize_components()
        
        # System state
        self.running = False
        self.shutdown_event = threading.Event()
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Enhanced Trading System initialized successfully")
    
    def _initialize_components(self):
        """Initialize all system components"""
        try:
            # Database and caching
            db_config = self.config.get_database_config()
            cache_config = self.config.get_cache_config()
            self.db_manager = DatabaseManager(db_config.url, cache_config)
            
            # Risk management
            risk_config = self.config.get_risk_config()
            self.risk_manager = AdvancedRiskManager()
            
            # Technical analysis
            self.indicators = AdvancedIndicators()
            self.mtf_analyzer = MultiTimeframeAnalyzer()
            
            # Machine learning
            ml_config = self.config.get_ml_config()
            self.ml_ensemble = MLEnsemble()
            
            # Alert system
            alert_config = self.config.get_alert_config()
            self.alert_system = SmartAlertSystem(alert_config.__dict__)
            
            # Performance monitoring
            monitoring_config = self.config.get_monitoring_config()
            if monitoring_config.enabled:
                self.performance_monitor = PerformanceMonitor(self.db_manager)
            else:
                self.performance_monitor = None
            
            # Backtesting
            backtest_config = self.config.get_backtest_config()
            self.backtester = AdvancedBacktester(
                initial_capital=backtest_config.initial_capital,
                commission=backtest_config.commission
            )
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            raise
    
    async def start(self):
        """Start the trading system"""
        try:
            logger.info("Starting Enhanced Trading System...")
            self.running = True
            
            # Start performance monitoring
            if self.performance_monitor:
                self.performance_monitor.start_monitoring()
            
            # Start main trading loop
            await self._main_trading_loop()
            
        except Exception as e:
            logger.error(f"Error starting trading system: {e}")
            await self.stop()
    
    async def stop(self):
        """Stop the trading system"""
        logger.info("Stopping Enhanced Trading System...")
        self.running = False
        self.shutdown_event.set()
        
        # Stop performance monitoring
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("Enhanced Trading System stopped")
    
    async def _main_trading_loop(self):
        """Main trading loop"""
        trading_config = self.config.get_trading_config()
        symbols = trading_config.symbols
        
        logger.info(f"Starting trading loop for {len(symbols)} symbols")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                # Process each symbol
                tasks = []
                for symbol in symbols:
                    task = asyncio.create_task(self._process_symbol(symbol))
                    tasks.append(task)
                
                # Wait for all symbol processing to complete
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # Wait before next iteration
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _process_symbol(self, symbol: str):
        """Process a single symbol"""
        try:
            logger.debug(f"Processing symbol: {symbol}")
            
            # Fetch market data
            market_data = await self._fetch_market_data(symbol)
            if market_data is None or len(market_data) < 50:
                logger.warning(f"Insufficient data for {symbol}")
                return
            
            # Multi-timeframe analysis
            mtf_analysis = await self._perform_mtf_analysis(symbol, market_data)
            
            # ML predictions
            ml_prediction = await self._get_ml_prediction(symbol, market_data)
            
            # Risk assessment
            risk_assessment = await self._assess_risk(symbol, market_data)
            
            # Generate trading signal
            signal = await self._generate_signal(
                symbol, market_data, mtf_analysis, ml_prediction, risk_assessment
            )
            
            # Process signal
            if signal:
                await self._process_signal(signal)
            
        except Exception as e:
            logger.error(f"Error processing symbol {symbol}: {e}")
    
    async def _fetch_market_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Fetch market data for symbol"""
        try:
            # Try to get from database first
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            data = self.db_manager.get_market_data(
                symbol=symbol,
                timeframe='4h',
                start_date=start_date,
                end_date=end_date,
                limit=200
            )
            
            if data is not None and len(data) > 100:
                return data
            
            # If not enough data in database, fetch from exchange
            # This would be implemented with actual exchange API
            logger.info(f"Fetching fresh data for {symbol}")
            
            # For now, generate sample data
            dates = pd.date_range(end=datetime.now(), periods=200, freq='4H')
            np.random.seed(hash(symbol) % 2**32)
            
            close_prices = 50000 + np.cumsum(np.random.randn(200) * 100)
            sample_data = pd.DataFrame({
                'open': close_prices + np.random.randn(200) * 50,
                'high': close_prices + np.abs(np.random.randn(200) * 100),
                'low': close_prices - np.abs(np.random.randn(200) * 100),
                'close': close_prices,
                'volume': np.random.randint(1000, 10000, 200)
            }, index=dates)
            
            # Add technical indicators
            sample_data = self._add_technical_indicators(sample_data)
            
            # Store in database
            self.db_manager.store_market_data(symbol, '4h', sample_data)
            
            return sample_data
            
        except Exception as e:
            logger.error(f"Error fetching market data for {symbol}: {e}")
            return None
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to market data"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            # Bollinger Bands
            df = self.indicators.bollinger_bands(df)
            
            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(20).mean()
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding technical indicators: {e}")
            return df
    
    async def _perform_mtf_analysis(self, symbol: str, data: pd.DataFrame) -> Dict:
        """Perform multi-timeframe analysis"""
        try:
            # This would normally analyze multiple timeframes
            # For now, we'll simulate the analysis
            
            current_price = data['close'].iloc[-1]
            rsi = data['rsi'].iloc[-1]
            macd = data['macd'].iloc[-1]
            
            # Determine trend
            sma_20 = data['close'].rolling(20).mean().iloc[-1]
            sma_50 = data['close'].rolling(50).mean().iloc[-1]
            
            if current_price > sma_20 > sma_50:
                trend = "uptrend"
                trend_strength = 0.8
            elif current_price < sma_20 < sma_50:
                trend = "downtrend"
                trend_strength = 0.8
            else:
                trend = "sideways"
                trend_strength = 0.4
            
            analysis = {
                'trend': trend,
                'trend_strength': trend_strength,
                'rsi': rsi,
                'macd': macd,
                'support': data['low'].rolling(20).min().iloc[-1],
                'resistance': data['high'].rolling(20).max().iloc[-1]
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in MTF analysis for {symbol}: {e}")
            return {}
    
    async def _get_ml_prediction(self, symbol: str, data: pd.DataFrame) -> Dict:
        """Get ML ensemble prediction"""
        try:
            # Check if models are trained
            if not self.ml_ensemble.is_trained:
                logger.info("Training ML models...")
                df_with_features = self.ml_ensemble.prepare_features(data)
                performance = self.ml_ensemble.train_models(df_with_features)
                
                if self.performance_monitor:
                    for model_name, score in performance.items():
                        self.performance_monitor.update_model_performance(
                            model_name, {'accuracy': score}
                        )
            
            # Get prediction
            prediction = self.ml_ensemble.predict(data)
            
            if prediction:
                return {
                    'direction': prediction.direction,
                    'confidence': prediction.confidence,
                    'prediction': prediction.final_prediction,
                    'individual_predictions': [
                        {
                            'model': pred.model_name,
                            'prediction': pred.prediction,
                            'confidence': pred.confidence
                        }
                        for pred in prediction.individual_predictions
                    ]
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting ML prediction for {symbol}: {e}")
            return {}
    
    async def _assess_risk(self, symbol: str, data: pd.DataFrame) -> Dict:
        """Assess risk for the symbol"""
        try:
            risk_assessment = self.risk_manager.assess_market_risk(data)
            
            # Add position sizing calculation
            current_price = data['close'].iloc[-1]
            account_balance = 100000  # This would come from portfolio manager
            
            # Calculate dynamic stop loss
            stop_loss = self.risk_manager.calculate_dynamic_stop_loss(
                data, current_price, 'long'
            )
            
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                account_balance, current_price, stop_loss, RiskLevel.MODERATE
            )
            
            risk_assessment.update({
                'stop_loss': stop_loss,
                'position_size': position_size,
                'risk_reward_ratio': 2.0
            })
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Error assessing risk for {symbol}: {e}")
            return {}
    
    async def _generate_signal(self, symbol: str, data: pd.DataFrame,
                             mtf_analysis: Dict, ml_prediction: Dict,
                             risk_assessment: Dict) -> Optional[Dict]:
        """Generate trading signal"""
        try:
            current_price = data['close'].iloc[-1]
            
            # Combine all analysis
            signal_strength = 0
            confidence_factors = []
            
            # MTF analysis contribution
            if mtf_analysis.get('trend') == 'uptrend':
                signal_strength += 1
                confidence_factors.append(mtf_analysis.get('trend_strength', 0))
            elif mtf_analysis.get('trend') == 'downtrend':
                signal_strength -= 1
                confidence_factors.append(mtf_analysis.get('trend_strength', 0))
            
            # ML prediction contribution
            ml_direction = ml_prediction.get('direction', 'neutral')
            ml_confidence = ml_prediction.get('confidence', 0)
            
            if ml_direction == 'up' and ml_confidence > 0.7:
                signal_strength += 2
                confidence_factors.append(ml_confidence)
            elif ml_direction == 'down' and ml_confidence > 0.7:
                signal_strength -= 2
                confidence_factors.append(ml_confidence)
            
            # Risk assessment contribution
            overall_risk = risk_assessment.get('overall_risk', 'moderate')
            if overall_risk == 'low':
                signal_strength += 0.5
            elif overall_risk == 'high':
                signal_strength -= 0.5
            
            # Generate signal if strength is sufficient
            if abs(signal_strength) >= 2:
                direction = 'buy' if signal_strength > 0 else 'sell'
                confidence = np.mean(confidence_factors) if confidence_factors else 0.5
                
                signal = {
                    'symbol': symbol,
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'confidence': confidence,
                    'entry_price': current_price,
                    'stop_loss': risk_assessment.get('stop_loss', current_price * 0.95),
                    'target_price': current_price * 1.04 if direction == 'buy' else current_price * 0.96,
                    'position_size': risk_assessment.get('position_size', 0),
                    'signal_type': 'enhanced_system',
                    'mtf_analysis': mtf_analysis,
                    'ml_prediction': ml_prediction,
                    'risk_assessment': risk_assessment
                }
                
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return None
    
    async def _process_signal(self, signal: Dict):
        """Process generated signal"""
        try:
            logger.info(f"Processing signal: {signal['symbol']} {signal['direction']} "
                       f"(confidence: {signal['confidence']:.2%})")
            
            # Store signal in database
            self.db_manager.store_trading_signal(signal)
            
            # Check if signal meets alert criteria
            alert = self.alert_system.process_ml_prediction(
                signal['symbol'], signal['ml_prediction']
            )
            
            if alert:
                # Send alert
                await self.alert_system.send_alert(alert)
            
            # Track signal performance
            if self.performance_monitor:
                signal_id = self.performance_monitor.track_signal(signal)
                logger.info(f"Started tracking signal: {signal_id}")
            
            # Here you would implement actual trading logic
            # For now, we'll just log the signal
            logger.info(f"Signal generated for {signal['symbol']}: "
                       f"{signal['direction']} at {signal['entry_price']:.2f}")
            
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown_event.set()
    
    async def run_backtest(self, symbol: str, strategy_name: str = "enhanced_system"):
        """Run backtest for the enhanced system"""
        try:
            logger.info(f"Running backtest for {symbol} with {strategy_name} strategy")
            
            # Fetch historical data
            backtest_config = self.config.get_backtest_config()
            start_date = datetime.strptime(backtest_config.start_date, '%Y-%m-%d')
            end_date = datetime.strptime(backtest_config.end_date, '%Y-%m-%d')
            
            data = self.db_manager.get_market_data(
                symbol=symbol,
                timeframe='4h',
                start_date=start_date,
                end_date=end_date
            )
            
            if data is None or len(data) < 100:
                logger.error(f"Insufficient data for backtesting {symbol}")
                return None
            
            # Define strategy function
            def enhanced_strategy(df: pd.DataFrame) -> Optional[Dict]:
                if len(df) < 50:
                    return None
                
                # Simplified strategy for backtesting
                rsi = df['rsi'].iloc[-1] if 'rsi' in df.columns else 50
                macd = df['macd'].iloc[-1] if 'macd' in df.columns else 0
                
                if rsi < 30 and macd > 0:
                    return {
                        'action': 'enter',
                        'side': 'long',
                        'reason': 'RSI oversold + MACD bullish',
                        'risk_percent': 0.02
                    }
                elif rsi > 70 or macd < 0:
                    return {
                        'action': 'exit',
                        'reason': 'RSI overbought or MACD bearish'
                    }
                
                return None
            
            # Run backtest
            results = self.backtester.run_backtest(data, enhanced_strategy)
            
            if results:
                # Generate report
                report = self.backtester.generate_performance_report(results)
                logger.info(f"Backtest completed for {symbol}")
                print(report)
                
                return results
            
            return None
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return None

# Main execution
async def main():
    """Main function"""
    try:
        # Initialize system
        system = EnhancedTradingSystem()
        
        # Start system
        await system.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        if 'system' in locals():
            await system.stop()

if __name__ == "__main__":
    asyncio.run(main())

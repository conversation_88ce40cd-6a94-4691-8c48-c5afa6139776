# نظام إشارات تداول العملات المشفرة
# Crypto Trading Signal System

## نظرة عامة / Overview

هذا النظام هو نظام متقدم لتحليل وتتبع إشارات تداول العملات المشفرة باستخدام مجموعة من المؤشرات الفنية والتحليلات الإحصائية.

This is an advanced cryptocurrency trading signal system that uses a combination of technical indicators and statistical analysis to identify high-probability trading opportunities.

## مكونات النظام / System Components

### 1. جمع البيانات / Data Collection
- **الملف:** `src/data_collector.py`
- يجمع بيانات تاريخية للشموع كل 4 ساعات من Binance
- يحمل 2500 شمعة لكل رمز
- يحسب المؤشرات الفنية
- يعالج حدود معدل API
- يستخدم التنفيذ المتعدد للتحميل السريع

### 2. تحليل الإشارات / Signal Analysis
- **الملف:** `src/signal_analyzer.py`
- يحلل مجموعات المؤشرات الفنية
- يحسب احتمالات النجاح والعوائد المتوقعة
- يستخدم مقاييس إحصائية متقدمة (نسبة شارب وغيرها)
- يولد درجات مرجحة لكل مجموعة
- يخرج النتائج إلى ملف JSON

### 3. اختيار الاستراتيجية / Strategy Selection
- **الملف:** `src/strategy_selector.py`
- يصفي المجموعات بناءً على معايير الجودة
- يصنف المجموعات باستخدام نظام تسجيل شامل
- يختار أفضل 5 مجموعات موثوقة
- يخرج النتائج إلى ملف JSON

### 4. المراقبة المتقدمة / Advanced Monitoring
- **الملف:** `src/monitor.py`
- يراقب الأسواق في الوقت الفعلي
- يطبق مجموعات المؤشرات المختارة
- يرسل تنبيهات عند اكتشاف فرص عالية الاحتمال
- يتضمن فترات تهدئة لمنع تكرار التنبيهات
- يتكامل مع Telegram للإشعارات

## المؤشرات الفنية / Technical Indicators

1. **RSI (مؤشر القوة النسبية):**
   - يحدد حالات التشبع في البيع
   - العتبة الافتراضية: 30

2. **MACD (تقارب وتباعد المتوسطات المتحركة):**
   - يحدد تغيرات الاتجاه
   - يستخدم التقاطعات الصاعدة في المنطقة السلبية

3. **OBV (الحجم على التوازن):**
   - يؤكد تحركات السعر مع الحجم
   - يستخدم المتوسط المتحرك البسيط لمدة 20 فترة للتأكيد

4. **ADX (مؤشر الاتجاه المتوسط):**
   - يقيس قوة الاتجاه
   - الحد الأدنى للعتبة: 25

5. **CMF (تدفق المال لشايكن):**
   - يقيس ضغط البيع والشراء
   - يؤكد الاتجاهات القائمة على الحجم

## معلمات النظام / System Parameters

### جمع البيانات / Data Collection
- الإطار الزمني: 4 ساعات
- التاريخ: 2500 شمعة
- التحميلات المتزامنة: 5 رموز

### تحليل الإشارات / Signal Analysis
- الحد الأدنى لمعدل النجاح: 55%
- الحد الأدنى للربح المستهدف: 3%
- الحد الأدنى لحجم العينة: 30 إشارة

### المراقبة / Monitoring
- فترة التهدئة للتنبيهات: 12 ساعة لكل رمز
- محاولات إعادة المحاولة لـ API: 3
- فترة التحديث: 5 دقائق

## التحسينات المقترحة / Suggested Improvements

1. **تحسينات في التحليل / Analysis Improvements:**
   - إضافة تحليل العوامل الخارجية (أخبار السوق، المشاعر)
   - تحسين خوارزميات اكتشاف الأنماط
   - دمج التحليل الأساسي مع التحليل الفني

2. **تحسينات في البنية / Architecture Improvements:**
   - تحويل النظام إلى بنية خدمات مصغرة
   - تحسين قابلية التوسع والأداء
   - إضافة واجهة مستخدم على الويب

3. **تحسينات في إدارة المخاطر / Risk Management:**
   - إضافة نموذج متقدم لإدارة المخاطر
   - تحسين استراتيجيات وقف الخسارة
   - تنفيذ إدارة رأس المال التكيفية

4. **تحسينات في التنبيهات / Alert Improvements:**
   - إضافة تصفية التنبيهات الذكية
   - تحسين جودة الإشارات
   - دعم منصات إضافية للتنبيهات

## طريقة التشغيل / How to Run

1. **إعداد البيئة / Setup Environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # أو / or
   venv\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

2. **تكوين النظام / Configure System:**
   - إنشاء ملف `.env` مع بيانات اعتماد Telegram
   - تحديث `config/symbols.txt` بأزواج العملات المشفرة المطلوبة
   - ضبط المعلمات في `config/settings.json`

3. **تشغيل النظام / Run System:**
   ```bash
   # 1. جمع البيانات / Collect Data
   python src/data_collector.py

   # 2. تحليل الإشارات / Analyze Signals
   python src/signal_analyzer.py

   # 3. اختيار الاستراتيجية / Select Strategy
   python src/strategy_selector.py

   # 4. بدء المراقبة / Start Monitoring
   python src/monitor.py
   ```

## ملاحظات مهمة / Important Notes

1. **إدارة المخاطر / Risk Management:**
   - لا تتداول فقط بناءً على الإشارات الفنية
   - استخدم حجم المركز المناسب
   - ضع أوامر وقف الخسارة
   - راعي ظروف السوق

2. **صيانة النظام / System Maintenance:**
   - تحديث البيانات التاريخية بانتظام
   - مراقبة مقاييس جودة الإشارات
   - ضبط المعلمات عند الحاجة
   - تحديث التبعيات

3. **المراقبة / Monitoring:**
   - تحقق من سجلات النظام بانتظام
   - تحقق من اتصال Telegram
   - راقب حدود معدل API
   - نسخ ملفات التكوين احتياطياً

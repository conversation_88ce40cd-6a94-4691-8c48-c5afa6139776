"""
Advanced Performance Monitoring System for Crypto Trading
Tracks signal performance, model accuracy, and system health
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
import asyncio
from collections import defaultdict, deque
import threading
import time
import psutil
import gc

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SignalPerformance:
    """Signal performance metrics"""
    signal_id: str
    symbol: str
    timestamp: datetime
    signal_type: str
    direction: str
    confidence: float
    entry_price: float
    current_price: float
    target_price: float
    stop_loss: float
    duration_hours: float
    status: str  # 'active', 'hit_target', 'hit_stop', 'expired'
    pnl_percent: float
    max_favorable: float
    max_adverse: float

@dataclass
class ModelPerformance:
    """ML model performance metrics"""
    model_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    sharpe_ratio: float
    total_predictions: int
    correct_predictions: int
    avg_confidence: float
    last_updated: datetime

@dataclass
class SystemHealth:
    """System health metrics"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    api_response_time: float
    database_response_time: float
    cache_hit_rate: float
    active_connections: int
    error_rate: float
    uptime_hours: float

class PerformanceMonitor:
    """Advanced performance monitoring system"""
    
    def __init__(self, db_manager=None):
        """Initialize performance monitor"""
        self.db_manager = db_manager
        self.signal_tracker = {}
        self.model_metrics = {}
        self.system_metrics = deque(maxlen=1440)  # 24 hours of minute data
        self.alert_performance = defaultdict(list)
        self.running = False
        self.monitor_thread = None
        self.start_time = datetime.now()
        
        # Performance thresholds
        self.thresholds = {
            'min_signal_accuracy': 0.60,
            'min_model_accuracy': 0.65,
            'max_cpu_usage': 80.0,
            'max_memory_usage': 85.0,
            'max_response_time': 5.0,
            'min_cache_hit_rate': 0.70
        }
        
        logger.info("Performance monitor initialized")
    
    def start_monitoring(self):
        """Start continuous performance monitoring"""
        if self.running:
            logger.warning("Performance monitoring already running")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Performance monitoring stopped")
    
    def track_signal(self, signal_data: Dict) -> str:
        """
        Start tracking a new signal
        
        Args:
            signal_data: Signal information
            
        Returns:
            Signal tracking ID
        """
        try:
            signal_id = f"{signal_data['symbol']}_{signal_data['timestamp'].strftime('%Y%m%d_%H%M%S')}"
            
            performance = SignalPerformance(
                signal_id=signal_id,
                symbol=signal_data['symbol'],
                timestamp=signal_data['timestamp'],
                signal_type=signal_data.get('signal_type', 'unknown'),
                direction=signal_data['direction'],
                confidence=signal_data['confidence'],
                entry_price=signal_data['entry_price'],
                current_price=signal_data['entry_price'],
                target_price=signal_data.get('target_price', 0),
                stop_loss=signal_data.get('stop_loss', 0),
                duration_hours=0,
                status='active',
                pnl_percent=0,
                max_favorable=0,
                max_adverse=0
            )
            
            self.signal_tracker[signal_id] = performance
            logger.info(f"Started tracking signal: {signal_id}")
            
            return signal_id
            
        except Exception as e:
            logger.error(f"Error tracking signal: {e}")
            return ""
    
    def update_signal(self, signal_id: str, current_price: float) -> bool:
        """
        Update signal performance with current price
        
        Args:
            signal_id: Signal tracking ID
            current_price: Current market price
            
        Returns:
            Success status
        """
        try:
            if signal_id not in self.signal_tracker:
                logger.warning(f"Signal {signal_id} not found in tracker")
                return False
            
            signal = self.signal_tracker[signal_id]
            signal.current_price = current_price
            
            # Calculate duration
            signal.duration_hours = (datetime.now() - signal.timestamp).total_seconds() / 3600
            
            # Calculate P&L
            if signal.direction.lower() == 'buy':
                signal.pnl_percent = (current_price - signal.entry_price) / signal.entry_price
            else:  # sell
                signal.pnl_percent = (signal.entry_price - current_price) / signal.entry_price
            
            # Track max favorable/adverse moves
            if signal.pnl_percent > signal.max_favorable:
                signal.max_favorable = signal.pnl_percent
            if signal.pnl_percent < signal.max_adverse:
                signal.max_adverse = signal.pnl_percent
            
            # Check if signal hit target or stop loss
            if signal.target_price > 0:
                if signal.direction.lower() == 'buy' and current_price >= signal.target_price:
                    signal.status = 'hit_target'
                elif signal.direction.lower() == 'sell' and current_price <= signal.target_price:
                    signal.status = 'hit_target'
            
            if signal.stop_loss > 0:
                if signal.direction.lower() == 'buy' and current_price <= signal.stop_loss:
                    signal.status = 'hit_stop'
                elif signal.direction.lower() == 'sell' and current_price >= signal.stop_loss:
                    signal.status = 'hit_stop'
            
            # Check for expiration (24 hours default)
            if signal.duration_hours > 24 and signal.status == 'active':
                signal.status = 'expired'
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating signal {signal_id}: {e}")
            return False
    
    def close_signal(self, signal_id: str, final_price: float, outcome: str) -> bool:
        """
        Close signal tracking
        
        Args:
            signal_id: Signal tracking ID
            final_price: Final exit price
            outcome: 'profit', 'loss', 'breakeven'
            
        Returns:
            Success status
        """
        try:
            if signal_id not in self.signal_tracker:
                return False
            
            signal = self.signal_tracker[signal_id]
            signal.current_price = final_price
            signal.status = 'closed'
            
            # Final P&L calculation
            if signal.direction.lower() == 'buy':
                signal.pnl_percent = (final_price - signal.entry_price) / signal.entry_price
            else:
                signal.pnl_percent = (signal.entry_price - final_price) / signal.entry_price
            
            # Store in database if available
            if self.db_manager:
                self._store_signal_performance(signal)
            
            logger.info(f"Closed signal {signal_id}: {outcome} ({signal.pnl_percent:.2%})")
            return True
            
        except Exception as e:
            logger.error(f"Error closing signal {signal_id}: {e}")
            return False
    
    def update_model_performance(self, model_name: str, metrics: Dict) -> bool:
        """
        Update ML model performance metrics
        
        Args:
            model_name: Name of the model
            metrics: Performance metrics dictionary
            
        Returns:
            Success status
        """
        try:
            performance = ModelPerformance(
                model_name=model_name,
                accuracy=metrics.get('accuracy', 0),
                precision=metrics.get('precision', 0),
                recall=metrics.get('recall', 0),
                f1_score=metrics.get('f1_score', 0),
                sharpe_ratio=metrics.get('sharpe_ratio', 0),
                total_predictions=metrics.get('total_predictions', 0),
                correct_predictions=metrics.get('correct_predictions', 0),
                avg_confidence=metrics.get('avg_confidence', 0),
                last_updated=datetime.now()
            )
            
            self.model_metrics[model_name] = performance
            
            # Check if performance is below threshold
            if performance.accuracy < self.thresholds['min_model_accuracy']:
                logger.warning(f"Model {model_name} accuracy below threshold: {performance.accuracy:.2%}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating model performance: {e}")
            return False
    
    def get_signal_statistics(self, symbol: str = None, 
                            days: int = 30) -> Dict[str, Any]:
        """
        Get signal performance statistics
        
        Args:
            symbol: Filter by symbol (optional)
            days: Number of days to analyze
            
        Returns:
            Statistics dictionary
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Filter signals
            filtered_signals = []
            for signal in self.signal_tracker.values():
                if signal.timestamp >= cutoff_date:
                    if symbol is None or signal.symbol == symbol:
                        filtered_signals.append(signal)
            
            if not filtered_signals:
                return {}
            
            # Calculate statistics
            total_signals = len(filtered_signals)
            profitable_signals = len([s for s in filtered_signals if s.pnl_percent > 0])
            losing_signals = len([s for s in filtered_signals if s.pnl_percent < 0])
            
            win_rate = profitable_signals / total_signals if total_signals > 0 else 0
            
            avg_pnl = np.mean([s.pnl_percent for s in filtered_signals])
            avg_duration = np.mean([s.duration_hours for s in filtered_signals])
            
            profitable_pnls = [s.pnl_percent for s in filtered_signals if s.pnl_percent > 0]
            losing_pnls = [s.pnl_percent for s in filtered_signals if s.pnl_percent < 0]
            
            avg_win = np.mean(profitable_pnls) if profitable_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 0
            
            profit_factor = abs(sum(profitable_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')
            
            # Confidence analysis
            high_conf_signals = [s for s in filtered_signals if s.confidence > 0.8]
            high_conf_win_rate = len([s for s in high_conf_signals if s.pnl_percent > 0]) / len(high_conf_signals) if high_conf_signals else 0
            
            statistics = {
                'total_signals': total_signals,
                'profitable_signals': profitable_signals,
                'losing_signals': losing_signals,
                'win_rate': win_rate,
                'avg_pnl': avg_pnl,
                'avg_duration_hours': avg_duration,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'high_confidence_signals': len(high_conf_signals),
                'high_confidence_win_rate': high_conf_win_rate,
                'best_signal_pnl': max([s.pnl_percent for s in filtered_signals]) if filtered_signals else 0,
                'worst_signal_pnl': min([s.pnl_percent for s in filtered_signals]) if filtered_signals else 0
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"Error calculating signal statistics: {e}")
            return {}
    
    def get_model_rankings(self) -> List[Dict]:
        """Get model performance rankings"""
        try:
            rankings = []
            
            for model_name, performance in self.model_metrics.items():
                score = (
                    performance.accuracy * 0.4 +
                    performance.f1_score * 0.3 +
                    min(performance.sharpe_ratio / 2, 1) * 0.3
                )
                
                rankings.append({
                    'model_name': model_name,
                    'score': score,
                    'accuracy': performance.accuracy,
                    'f1_score': performance.f1_score,
                    'sharpe_ratio': performance.sharpe_ratio,
                    'total_predictions': performance.total_predictions,
                    'last_updated': performance.last_updated
                })
            
            # Sort by score
            rankings.sort(key=lambda x: x['score'], reverse=True)
            
            return rankings
            
        except Exception as e:
            logger.error(f"Error getting model rankings: {e}")
            return []
    
    def get_system_health(self) -> SystemHealth:
        """Get current system health metrics"""
        try:
            # CPU and memory usage
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            # Calculate uptime
            uptime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
            
            # Simulate other metrics (in real implementation, these would be measured)
            api_response_time = np.random.uniform(0.1, 2.0)
            database_response_time = np.random.uniform(0.05, 1.0)
            cache_hit_rate = np.random.uniform(0.7, 0.95)
            active_connections = np.random.randint(5, 50)
            error_rate = np.random.uniform(0, 0.05)
            
            health = SystemHealth(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                api_response_time=api_response_time,
                database_response_time=database_response_time,
                cache_hit_rate=cache_hit_rate,
                active_connections=active_connections,
                error_rate=error_rate,
                uptime_hours=uptime_hours
            )
            
            return health
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return SystemHealth(0, 0, 0, 0, 0, 0, 0, 0, 0)
    
    def generate_performance_report(self, days: int = 7) -> str:
        """Generate comprehensive performance report"""
        try:
            report = "=" * 60 + "\n"
            report += "PERFORMANCE MONITORING REPORT\n"
            report += "=" * 60 + "\n\n"
            
            # Signal performance
            signal_stats = self.get_signal_statistics(days=days)
            if signal_stats:
                report += "SIGNAL PERFORMANCE\n"
                report += "-" * 20 + "\n"
                report += f"Total Signals: {signal_stats['total_signals']}\n"
                report += f"Win Rate: {signal_stats['win_rate']:.2%}\n"
                report += f"Average P&L: {signal_stats['avg_pnl']:.2%}\n"
                report += f"Profit Factor: {signal_stats['profit_factor']:.2f}\n"
                report += f"High Confidence Win Rate: {signal_stats['high_confidence_win_rate']:.2%}\n\n"
            
            # Model performance
            model_rankings = self.get_model_rankings()
            if model_rankings:
                report += "MODEL PERFORMANCE RANKINGS\n"
                report += "-" * 30 + "\n"
                for i, model in enumerate(model_rankings[:5], 1):
                    report += f"{i}. {model['model_name']}: {model['score']:.3f} "
                    report += f"(Acc: {model['accuracy']:.2%}, F1: {model['f1_score']:.3f})\n"
                report += "\n"
            
            # System health
            health = self.get_system_health()
            report += "SYSTEM HEALTH\n"
            report += "-" * 15 + "\n"
            report += f"CPU Usage: {health.cpu_usage:.1f}%\n"
            report += f"Memory Usage: {health.memory_usage:.1f}%\n"
            report += f"Disk Usage: {health.disk_usage:.1f}%\n"
            report += f"API Response Time: {health.api_response_time:.2f}s\n"
            report += f"Cache Hit Rate: {health.cache_hit_rate:.1%}\n"
            report += f"Uptime: {health.uptime_hours:.1f} hours\n\n"
            
            # Alerts and warnings
            report += "ALERTS & WARNINGS\n"
            report += "-" * 18 + "\n"
            
            warnings = []
            if signal_stats.get('win_rate', 0) < self.thresholds['min_signal_accuracy']:
                warnings.append(f"Signal accuracy below threshold: {signal_stats['win_rate']:.2%}")
            
            if health.cpu_usage > self.thresholds['max_cpu_usage']:
                warnings.append(f"High CPU usage: {health.cpu_usage:.1f}%")
            
            if health.memory_usage > self.thresholds['max_memory_usage']:
                warnings.append(f"High memory usage: {health.memory_usage:.1f}%")
            
            if health.cache_hit_rate < self.thresholds['min_cache_hit_rate']:
                warnings.append(f"Low cache hit rate: {health.cache_hit_rate:.1%}")
            
            if warnings:
                for warning in warnings:
                    report += f"⚠️  {warning}\n"
            else:
                report += "✅ All systems operating normally\n"
            
            report += "\n" + "=" * 60
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return "Error generating performance report"
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Collect system metrics
                health = self.get_system_health()
                self.system_metrics.append({
                    'timestamp': datetime.now(),
                    'health': health
                })
                
                # Update active signals
                self._update_active_signals()
                
                # Check for performance alerts
                self._check_performance_alerts(health)
                
                # Cleanup old data
                self._cleanup_old_signals()
                
                # Sleep for 1 minute
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)
    
    def _update_active_signals(self):
        """Update all active signals with current prices"""
        # This would fetch current prices and update signals
        # For now, we'll simulate price updates
        for signal_id, signal in self.signal_tracker.items():
            if signal.status == 'active':
                # Simulate price movement
                price_change = np.random.uniform(-0.02, 0.02)
                new_price = signal.current_price * (1 + price_change)
                self.update_signal(signal_id, new_price)
    
    def _check_performance_alerts(self, health: SystemHealth):
        """Check for performance alerts"""
        if health.cpu_usage > self.thresholds['max_cpu_usage']:
            logger.warning(f"High CPU usage detected: {health.cpu_usage:.1f}%")
        
        if health.memory_usage > self.thresholds['max_memory_usage']:
            logger.warning(f"High memory usage detected: {health.memory_usage:.1f}%")
        
        if health.api_response_time > self.thresholds['max_response_time']:
            logger.warning(f"Slow API response time: {health.api_response_time:.2f}s")
    
    def _cleanup_old_signals(self):
        """Remove old completed signals from memory"""
        cutoff_date = datetime.now() - timedelta(days=7)
        
        signals_to_remove = []
        for signal_id, signal in self.signal_tracker.items():
            if signal.timestamp < cutoff_date and signal.status != 'active':
                signals_to_remove.append(signal_id)
        
        for signal_id in signals_to_remove:
            del self.signal_tracker[signal_id]
        
        if signals_to_remove:
            logger.info(f"Cleaned up {len(signals_to_remove)} old signals")
    
    def _store_signal_performance(self, signal: SignalPerformance):
        """Store signal performance in database"""
        try:
            if not self.db_manager:
                return
            
            # This would store the signal performance in the database
            # Implementation depends on the database schema
            logger.debug(f"Stored signal performance: {signal.signal_id}")
            
        except Exception as e:
            logger.error(f"Error storing signal performance: {e}")

# Example usage
if __name__ == "__main__":
    # Initialize performance monitor
    monitor = PerformanceMonitor()
    
    # Start monitoring
    monitor.start_monitoring()
    
    # Simulate tracking a signal
    signal_data = {
        'symbol': 'BTC/USDT',
        'timestamp': datetime.now(),
        'signal_type': 'RSI_oversold',
        'direction': 'buy',
        'confidence': 0.85,
        'entry_price': 50000,
        'target_price': 52000,
        'stop_loss': 48000
    }
    
    signal_id = monitor.track_signal(signal_data)
    
    # Simulate price updates
    for i in range(10):
        new_price = 50000 + np.random.uniform(-1000, 1000)
        monitor.update_signal(signal_id, new_price)
        time.sleep(1)
    
    # Get statistics
    stats = monitor.get_signal_statistics()
    print("Signal Statistics:", stats)
    
    # Generate report
    report = monitor.generate_performance_report()
    print(report)
    
    # Stop monitoring
    monitor.stop_monitoring()

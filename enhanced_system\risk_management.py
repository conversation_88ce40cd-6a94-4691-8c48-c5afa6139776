"""
Enhanced Risk Management System for Crypto Trading
Provides advanced position sizing, stop-loss, and risk assessment capabilities
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk level enumeration"""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

@dataclass
class RiskParameters:
    """Risk management parameters"""
    max_risk_per_trade: float = 0.02  # 2% max risk per trade
    max_portfolio_risk: float = 0.10  # 10% max portfolio risk
    max_correlation_exposure: float = 0.30  # 30% max correlated positions
    stop_loss_atr_multiplier: float = 2.0  # ATR multiplier for stop loss
    take_profit_ratio: float = 2.0  # Risk:Reward ratio
    max_consecutive_losses: int = 3  # Max consecutive losses before reducing size

class AdvancedRiskManager:
    """Advanced risk management system"""
    
    def __init__(self, risk_params: RiskParameters = None):
        """Initialize risk manager"""
        self.risk_params = risk_params or RiskParameters()
        self.trade_history = []
        self.current_positions = {}
        self.consecutive_losses = 0
        
    def calculate_position_size(self, 
                              account_balance: float,
                              entry_price: float,
                              stop_loss_price: float,
                              risk_level: RiskLevel = RiskLevel.MODERATE) -> float:
        """
        Calculate optimal position size based on risk parameters
        
        Args:
            account_balance: Total account balance
            entry_price: Entry price for the position
            stop_loss_price: Stop loss price
            risk_level: Risk level (conservative, moderate, aggressive)
            
        Returns:
            Position size in base currency
        """
        try:
            # Adjust risk based on consecutive losses
            risk_multiplier = self._get_risk_multiplier()
            
            # Calculate risk amount
            base_risk = self._get_base_risk_by_level(risk_level)
            adjusted_risk = base_risk * risk_multiplier
            risk_amount = account_balance * adjusted_risk
            
            # Calculate stop loss distance
            stop_loss_distance = abs(entry_price - stop_loss_price) / entry_price
            
            # Calculate position size
            position_size = risk_amount / stop_loss_distance
            
            # Apply portfolio risk limits
            max_position_size = account_balance * self.risk_params.max_portfolio_risk
            position_size = min(position_size, max_position_size)
            
            logger.info(f"Calculated position size: {position_size:.2f} (Risk: {adjusted_risk:.1%})")
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def calculate_dynamic_stop_loss(self, 
                                  df: pd.DataFrame,
                                  entry_price: float,
                                  position_type: str = "long") -> float:
        """
        Calculate dynamic stop loss using ATR
        
        Args:
            df: DataFrame with OHLCV data
            entry_price: Entry price
            position_type: "long" or "short"
            
        Returns:
            Stop loss price
        """
        try:
            # Calculate ATR
            atr = self._calculate_atr(df)
            current_atr = atr.iloc[-1]
            
            # Calculate stop loss
            if position_type.lower() == "long":
                stop_loss = entry_price - (current_atr * self.risk_params.stop_loss_atr_multiplier)
            else:
                stop_loss = entry_price + (current_atr * self.risk_params.stop_loss_atr_multiplier)
                
            logger.info(f"Dynamic stop loss calculated: {stop_loss:.4f} (ATR: {current_atr:.4f})")
            return stop_loss
            
        except Exception as e:
            logger.error(f"Error calculating dynamic stop loss: {e}")
            return entry_price * 0.95 if position_type.lower() == "long" else entry_price * 1.05
    
    def calculate_take_profit(self, 
                            entry_price: float,
                            stop_loss_price: float,
                            position_type: str = "long") -> float:
        """
        Calculate take profit based on risk:reward ratio
        
        Args:
            entry_price: Entry price
            stop_loss_price: Stop loss price
            position_type: "long" or "short"
            
        Returns:
            Take profit price
        """
        try:
            risk_distance = abs(entry_price - stop_loss_price)
            reward_distance = risk_distance * self.risk_params.take_profit_ratio
            
            if position_type.lower() == "long":
                take_profit = entry_price + reward_distance
            else:
                take_profit = entry_price - reward_distance
                
            logger.info(f"Take profit calculated: {take_profit:.4f} (R:R = 1:{self.risk_params.take_profit_ratio})")
            return take_profit
            
        except Exception as e:
            logger.error(f"Error calculating take profit: {e}")
            return entry_price * 1.06 if position_type.lower() == "long" else entry_price * 0.94
    
    def assess_market_risk(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Assess current market risk conditions
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dictionary with risk metrics
        """
        try:
            # Calculate volatility (ATR as percentage)
            atr = self._calculate_atr(df)
            volatility = (atr.iloc[-1] / df['close'].iloc[-1]) * 100
            
            # Calculate trend strength (ADX)
            adx = self._calculate_adx(df)
            trend_strength = adx.iloc[-1]
            
            # Calculate volume trend
            volume_sma = df['volume'].rolling(20).mean()
            volume_trend = df['volume'].iloc[-1] / volume_sma.iloc[-1]
            
            # Calculate price momentum
            returns = df['close'].pct_change(20).iloc[-1] * 100
            
            risk_assessment = {
                'volatility': volatility,
                'trend_strength': trend_strength,
                'volume_trend': volume_trend,
                'momentum': returns,
                'overall_risk': self._calculate_overall_risk(volatility, trend_strength, volume_trend)
            }
            
            logger.info(f"Market risk assessment: {risk_assessment}")
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Error assessing market risk: {e}")
            return {'volatility': 50, 'trend_strength': 25, 'volume_trend': 1, 'momentum': 0, 'overall_risk': 'moderate'}
    
    def update_trailing_stop(self, 
                           current_price: float,
                           entry_price: float,
                           current_stop: float,
                           position_type: str = "long",
                           trailing_percent: float = 0.02) -> float:
        """
        Update trailing stop loss
        
        Args:
            current_price: Current market price
            entry_price: Original entry price
            current_stop: Current stop loss
            position_type: "long" or "short"
            trailing_percent: Trailing percentage
            
        Returns:
            Updated stop loss price
        """
        try:
            if position_type.lower() == "long":
                # Only move stop up for long positions
                new_stop = current_price * (1 - trailing_percent)
                updated_stop = max(current_stop, new_stop)
            else:
                # Only move stop down for short positions
                new_stop = current_price * (1 + trailing_percent)
                updated_stop = min(current_stop, new_stop)
            
            if updated_stop != current_stop:
                logger.info(f"Trailing stop updated: {current_stop:.4f} -> {updated_stop:.4f}")
            
            return updated_stop
            
        except Exception as e:
            logger.error(f"Error updating trailing stop: {e}")
            return current_stop
    
    def _get_risk_multiplier(self) -> float:
        """Get risk multiplier based on consecutive losses"""
        if self.consecutive_losses >= self.risk_params.max_consecutive_losses:
            return 0.5  # Reduce risk by 50%
        elif self.consecutive_losses >= 2:
            return 0.75  # Reduce risk by 25%
        else:
            return 1.0  # Normal risk
    
    def _get_base_risk_by_level(self, risk_level: RiskLevel) -> float:
        """Get base risk percentage by risk level"""
        risk_mapping = {
            RiskLevel.CONSERVATIVE: 0.01,  # 1%
            RiskLevel.MODERATE: 0.02,      # 2%
            RiskLevel.AGGRESSIVE: 0.03     # 3%
        }
        return risk_mapping.get(risk_level, 0.02)
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def _calculate_adx(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average Directional Index"""
        # Simplified ADX calculation
        high_diff = df['high'].diff()
        low_diff = df['low'].diff()
        
        plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
        
        atr = self._calculate_atr(df, period)
        plus_di = 100 * (pd.Series(plus_dm).rolling(period).mean() / atr)
        minus_di = 100 * (pd.Series(minus_dm).rolling(period).mean() / atr)
        
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        
        return adx
    
    def _calculate_overall_risk(self, volatility: float, trend_strength: float, volume_trend: float) -> str:
        """Calculate overall market risk level"""
        risk_score = 0
        
        # Volatility scoring
        if volatility > 5:
            risk_score += 2
        elif volatility > 3:
            risk_score += 1
        
        # Trend strength scoring
        if trend_strength < 20:
            risk_score += 1
        
        # Volume trend scoring
        if volume_trend < 0.8:
            risk_score += 1
        
        if risk_score >= 3:
            return "high"
        elif risk_score >= 2:
            return "moderate"
        else:
            return "low"

# Example usage and testing
if __name__ == "__main__":
    # Create risk manager
    risk_manager = AdvancedRiskManager()
    
    # Example calculations
    account_balance = 10000
    entry_price = 50000
    stop_loss_price = 48000
    
    position_size = risk_manager.calculate_position_size(
        account_balance, entry_price, stop_loss_price, RiskLevel.MODERATE
    )
    
    print(f"Recommended position size: ${position_size:.2f}")

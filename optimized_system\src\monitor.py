import os
import json
import ccxt
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import logging
import asyncio
import aiohttp
from typing import Dict, List, Optional
import ta
from rich.live import Live
from rich.table import Table
from rich.console import Console
from dotenv import load_dotenv

# Load environment variables and configuration
load_dotenv()
with open('config/settings.json', 'r') as f:
    CONFIG = json.load(f)

# Setup logging
logging.basicConfig(
    level=CONFIG['logging']['level'],
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(CONFIG['logging']['file_path']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
console = Console()

class CryptoMonitor:
    def __init__(self):
        """Initialize the crypto monitoring system"""
        self.exchange = ccxt.binance()
        self.timeframe = CONFIG['timeframe']
        self.limit = 100  # Number of candles to analyze
        
        # Load settings
        self.min_probability = CONFIG['monitoring']['min_probability']
        self.min_expected_return = CONFIG['monitoring']['min_expected_return']
        self.alert_cooldown = timedelta(hours=CONFIG['monitoring']['alert_cooldown_hours'])
        self.update_interval = CONFIG['monitoring']['update_interval_minutes']
        
        # Initialize state
        self.last_alerts = {}
        self.active_symbols = set()
        self.load_strategies()

    def load_strategies(self) -> None:
        """Load the selected trading strategies"""
        try:
            with open('config/top_strategies.json', 'r') as f:
                data = json.load(f)
                self.strategies = data['strategies']
            logger.info(f"Loaded {len(self.strategies)} trading strategies")
        except Exception as e:
            logger.error(f"Error loading strategies: {e}")
            self.strategies = []

    async def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for a DataFrame"""
        try:
            # RSI
            df['rsi'] = ta.momentum.RSIIndicator(
                df['close'], 
                window=CONFIG['indicators']['rsi']['period']
            ).rsi()

            # MACD
            macd = ta.trend.MACD(
                df['close'],
                window_fast=CONFIG['indicators']['macd']['fast_period'],
                window_slow=CONFIG['indicators']['macd']['slow_period'],
                window_sign=CONFIG['indicators']['macd']['signal_period']
            )
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()

            # OBV
            df['obv'] = ta.volume.OnBalanceVolumeIndicator(
                df['close'],
                df['volume']
            ).on_balance_volume()

            # ADX
            adx = ta.trend.ADXIndicator(
                df['high'],
                df['low'],
                df['close'],
                window=CONFIG['indicators']['adx']['period']
            )
            df['adx'] = adx.adx()
            df['adx_pos'] = adx.adx_pos()
            df['adx_neg'] = adx.adx_neg()

            # CMF
            df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
                df['high'],
                df['low'],
                df['close'],
                df['volume'],
                window=CONFIG['indicators']['cmf']['period']
            ).chaikin_money_flow()

            return df

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return None

    def check_strategy(self, df: pd.DataFrame, strategy: Dict) -> Dict:
        """Check if a symbol meets the criteria for a specific strategy"""
        try:
            current_price = df['close'].iloc[-1]
            
            # Check each indicator in the strategy
            signals = []
            for indicator, params in strategy['indicators'].items():
                if indicator == 'rsi':
                    if df['rsi'].iloc[-1] < params['threshold']:
                        signals.append(f"RSI: {df['rsi'].iloc[-1]:.1f}")
                
                elif indicator == 'macd':
                    if (df['macd'].iloc[-1] > df['macd_signal'].iloc[-1] and
                        df['macd'].iloc[-1] < 0):
                        signals.append(f"MACD: {df['macd'].iloc[-1]:.6f}")
                
                elif indicator == 'adx':
                    if df['adx'].iloc[-1] > params['threshold']:
                        signals.append(f"ADX: {df['adx'].iloc[-1]:.1f}")
                
                elif indicator == 'obv':
                    obv_sma = df['obv'].rolling(20).mean()
                    if df['obv'].iloc[-1] > obv_sma.iloc[-1]:
                        signals.append("OBV: Bullish")
                
                elif indicator == 'cmf':
                    if df['cmf'].iloc[-1] > params['threshold']:
                        signals.append(f"CMF: {df['cmf'].iloc[-1]:.3f}")
            
            # Calculate signal strength
            signal_strength = len(signals) / len(strategy['indicators'])
            
            return {
                'triggered': signal_strength >= 0.8,
                'signals': signals,
                'strength': signal_strength,
                'probability': strategy['success_rate'],
                'expected_return': strategy['average_return'],
                'holding_period': strategy['average_holding_period']
            }
            
        except Exception as e:
            logger.error(f"Error checking strategy: {e}")
            return None

    async def monitor_symbol(self, symbol: str) -> None:
        """Monitor a single symbol for signals"""
        try:
            # Skip if in cooldown
            if symbol in self.last_alerts:
                if datetime.now() - self.last_alerts[symbol] < self.alert_cooldown:
                    return
            
            # Fetch latest data
            ohlcv = self.exchange.fetch_ohlcv(
                symbol,
                timeframe=self.timeframe,
                limit=self.limit
            )
            
            if not ohlcv or len(ohlcv) < self.limit:
                logger.warning(f"Insufficient data for {symbol}")
                return
            
            # Prepare DataFrame
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = await self.calculate_indicators(df)
            
            if df is None:
                return
            
            # Check each strategy
            best_signal = None
            for strategy in self.strategies:
                signal = self.check_strategy(df, strategy)
                
                if signal and signal['triggered']:
                    if (not best_signal or 
                        signal['probability'] > best_signal['probability']):
                        best_signal = signal
            
            # Send alert if signal found
            if (best_signal and 
                best_signal['probability'] >= self.min_probability and
                best_signal['expected_return'] >= self.min_expected_return):
                
                await self.send_alert(symbol, df, best_signal)
                self.last_alerts[symbol] = datetime.now()
                self.active_symbols.add(symbol)
            
        except Exception as e:
            logger.error(f"Error monitoring {symbol}: {e}")

    async def send_alert(
        self,
        symbol: str,
        df: pd.DataFrame,
        signal: Dict
    ) -> None:
        """Send a Telegram alert"""
        try:
            current_price = df['close'].iloc[-1]
            predicted_price = current_price * (1 + signal['expected_return'])
            
            message = (
                f"🚨 *New Bullish Signal*\n\n"
                f"💰 *Symbol:* {symbol}\n"
                f"💵 *Current Price:* ${current_price:.6f}\n"
                f"🎯 *Target Price:* ${predicted_price:.6f}\n\n"
                f"📊 *Signal Analysis:*\n"
                f"• Probability: {signal['probability']:.1%}\n"
                f"• Expected Return: {signal['expected_return']:+.1%}\n"
                f"• Expected Time: {signal['holding_period']:.1f}h\n"
                f"• Signal Strength: {signal['strength']:.0%}\n\n"
                f"✅ *Active Signals:*\n"
                + "\n".join([f"• {s}" for s in signal['signals']])
            )
            
            bot_token = os.getenv('TELEGRAM_TOKEN')
            chat_id = os.getenv('TELEGRAM_CHAT_ID')
            
            if not bot_token or not chat_id:
                logger.error("Missing Telegram credentials")
                return
            
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            params = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params) as response:
                    if response.status == 200:
                        logger.info(f"Alert sent for {symbol}")
                    else:
                        logger.error(f"Failed to send alert: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error sending alert: {e}")

    def create_status_table(self) -> Table:
        """Create a status table for display"""
        table = Table(title="Crypto Monitor Status")
        
        table.add_column("Active Symbols", justify="right", style="cyan")
        table.add_column("Strategies", justify="right", style="magenta")
        table.add_column("Last Update", style="green")
        table.add_column("Next Update", style="yellow")
        
        now = datetime.now()
        next_update = now + timedelta(minutes=self.update_interval)
        
        table.add_row(
            str(len(self.active_symbols)),
            str(len(self.strategies)),
            now.strftime("%H:%M:%S"),
            next_update.strftime("%H:%M:%S")
        )
        
        return table

    async def run(self) -> None:
        """Main monitoring loop"""
        logger.info("Starting crypto monitor")
        
        # Load symbols
        with open('config/symbols.txt', 'r') as f:
            symbols = [
                line.strip()
                for line in f
                if line.strip() and not line.startswith('#')
            ]
        
        if not symbols:
            logger.error("No symbols to monitor")
            return
        
        logger.info(f"Monitoring {len(symbols)} symbols")
        
        while True:
            try:
                with Live(self.create_status_table(), refresh_per_second=1) as live:
                    start_time = datetime.now()
                    
                    # Monitor all symbols concurrently
                    tasks = [self.monitor_symbol(symbol) for symbol in symbols]
                    await asyncio.gather(*tasks)
                    
                    # Calculate and adjust sleep time
                    elapsed = (datetime.now() - start_time).total_seconds()
                    sleep_time = max(0, self.update_interval * 60 - elapsed)
                    
                    live.update(self.create_status_table())
                    await asyncio.sleep(sleep_time)
                    
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring cycle: {e}")
                await asyncio.sleep(60)

async def main():
    """Main entry point"""
    try:
        monitor = CryptoMonitor()
        await monitor.run()
    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

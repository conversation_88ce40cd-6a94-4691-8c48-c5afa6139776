import json
import logging
from typing import Dict, List
import pandas as pd

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('combo_selector.log'),
        logging.StreamHandler()
    ]
)

# Constants
MIN_SIGNALS = 30
MIN_SUCCESS_RATE = 0.55
MIN_SHARPE_RATIO = 1.0
OUTPUT_FILE = "top_combos.json"

def load_combo_data(filename: str = "combo_signal_weights.json") -> Dict:
    """Load and validate combination analysis data"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            data = json.load(f)
            logging.info(f"Loaded {len(data)} combinations from {filename}")
            return data
    except Exception as e:
        logging.error(f"Error loading combination data: {str(e)}")
        return {}

def filter_combinations(data: Dict) -> List[Dict]:
    """Filter combinations based on quality criteria"""
    filtered_combos = []
    
    for combo_name, stats in data.items():
        if (stats['total_signals'] >= MIN_SIGNALS and
            stats['success_probability'] >= MIN_SUCCESS_RATE and
            stats['sharpe_ratio'] >= MIN_SHARPE_RATIO):
            
            filtered_combos.append({
                'combo': combo_name,
                **stats
            })
    
    logging.info(f"Filtered down to {len(filtered_combos)} quality combinations")
    return filtered_combos

def rank_combinations(combos: List[Dict]) -> List[Dict]:
    """Rank combinations using a comprehensive scoring system"""
    for combo in combos:
        # Calculate composite score
        combo['score'] = (
            combo['success_probability'] * 0.35 +  # Success rate importance
            combo['average_return'] * 0.25 +       # Return importance
            (combo['sharpe_ratio'] / 5) * 0.20 +   # Risk-adjusted return (normalized)
            (combo['total_signals'] / 1000) * 0.10 + # Signal frequency (normalized)
            combo['weight'] * 0.10                  # Original weight
        )
    
    # Sort by composite score
    ranked_combos = sorted(combos, key=lambda x: x['score'], reverse=True)
    return ranked_combos[:5]

def format_combo_output(combo: Dict) -> str:
    """Format a combination's statistics for output"""
    return f"""
🔹 Combination: {combo['combo']}
📊 Statistics:
   • Signal Count: {combo['total_signals']}
   • Success Rate: {combo['success_probability']:.2%}
   • Avg Return: {combo['average_return']:.2%}
   • Sharpe Ratio: {combo['sharpe_ratio']:.2f}
   • Volatility: {combo['volatility']:.2%}
   • Composite Score: {combo['score']:.4f}"""

def main():
    """Main execution function"""
    try:
        # Load and filter combinations
        data = load_combo_data()
        if not data:
            logging.error("No combination data available")
            return
            
        # Filter and rank combinations
        filtered_combos = filter_combinations(data)
        if not filtered_combos:
            logging.error("No combinations met the quality criteria")
            return
            
        top_combos = rank_combinations(filtered_combos)
        
        # Save results
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(top_combos, f, indent=2)
        
        # Display results
        logging.info("\n=== Top 5 Technical Indicator Combinations ===")
        for combo in top_combos:
            print(format_combo_output(combo))
            
        logging.info(f"\nResults saved to {OUTPUT_FILE}")
        
    except Exception as e:
        logging.error(f"Error in main execution: {str(e)}")
        raise

if __name__ == "__main__":
    main()

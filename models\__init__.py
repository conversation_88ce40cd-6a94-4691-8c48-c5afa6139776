from .random_forest import <PERSON>Forest
from .sarimax import Sarimax
from .orbit import Orbit
from .LSTM import MyLSTM
from .GRU import MyGR<PERSON>
from .arima import MyARIMA
from .prophet import MyProphet
from .xgboost import MyXGboost
from .neural_prophet import Neural_Prophet


MODELS = {'random_forest': RandomForest,
          'sarimax': Sarimax,
          'orbit': Orbit,
          'lstm': MyLSTM,
          'gru': MyGRU,
          'arima': <PERSON><PERSON><PERSON><PERSON>,
          'prophet': <PERSON><PERSON>rophe<PERSON>,
          'xgboost': MyXGboost,
          'neural_prophet': <PERSON>eur<PERSON>_Prophet
          }

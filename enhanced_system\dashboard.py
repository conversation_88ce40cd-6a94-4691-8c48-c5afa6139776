"""
Interactive Dashboard for Enhanced Crypto Trading System
Built with Streamlit for real-time monitoring and analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from datetime import datetime, timedelta
import asyncio
import sys
import os

# Add enhanced_system to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our enhanced modules
from risk_management import AdvancedRiskManager, RiskLevel
from advanced_indicators import AdvancedIndicators
from multi_timeframe_analysis import MultiTimeframeAnalyzer
from ml_ensemble import MLEnsemble
from smart_alerts import SmartAlertSystem
from advanced_backtesting import AdvancedBacktester

# Page configuration
st.set_page_config(
    page_title="Enhanced Crypto Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .alert-high {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        padding: 1rem;
        border-radius: 0.5rem;
    }
    .alert-medium {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 1rem;
        border-radius: 0.5rem;
    }
    .alert-low {
        background-color: #e8f5e8;
        border-left: 4px solid #4caf50;
        padding: 1rem;
        border-radius: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

class TradingDashboard:
    """Main dashboard class"""
    
    def __init__(self):
        """Initialize dashboard components"""
        self.risk_manager = AdvancedRiskManager()
        self.indicators = AdvancedIndicators()
        self.mtf_analyzer = MultiTimeframeAnalyzer()
        self.ml_ensemble = MLEnsemble()
        self.alert_system = SmartAlertSystem()
        self.backtester = AdvancedBacktester()
        
        # Initialize session state
        if 'selected_symbol' not in st.session_state:
            st.session_state.selected_symbol = 'BTC/USDT'
        if 'portfolio_data' not in st.session_state:
            st.session_state.portfolio_data = self._initialize_portfolio()
    
    def run(self):
        """Run the main dashboard"""
        # Header
        st.markdown('<h1 class="main-header">🚀 Enhanced Crypto Trading Dashboard</h1>', 
                   unsafe_allow_html=True)
        
        # Sidebar
        self._render_sidebar()
        
        # Main content
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "📊 Market Analysis", 
            "🤖 ML Predictions", 
            "⚠️ Risk Management", 
            "🔔 Smart Alerts", 
            "📈 Backtesting", 
            "💼 Portfolio"
        ])
        
        with tab1:
            self._render_market_analysis()
        
        with tab2:
            self._render_ml_predictions()
        
        with tab3:
            self._render_risk_management()
        
        with tab4:
            self._render_smart_alerts()
        
        with tab5:
            self._render_backtesting()
        
        with tab6:
            self._render_portfolio()
    
    def _render_sidebar(self):
        """Render sidebar controls"""
        st.sidebar.header("🎛️ Control Panel")
        
        # Symbol selection
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT']
        st.session_state.selected_symbol = st.sidebar.selectbox(
            "Select Symbol", symbols, index=symbols.index(st.session_state.selected_symbol)
        )
        
        # Timeframe selection
        timeframes = ['1h', '4h', '1d', '1w']
        selected_timeframe = st.sidebar.selectbox("Timeframe", timeframes, index=1)
        
        # Risk settings
        st.sidebar.subheader("⚖️ Risk Settings")
        risk_level = st.sidebar.selectbox(
            "Risk Level", 
            [RiskLevel.CONSERVATIVE, RiskLevel.MODERATE, RiskLevel.AGGRESSIVE],
            index=1
        )
        
        account_balance = st.sidebar.number_input(
            "Account Balance ($)", 
            min_value=1000, 
            max_value=1000000, 
            value=100000, 
            step=1000
        )
        
        # Auto-refresh
        auto_refresh = st.sidebar.checkbox("Auto Refresh (30s)", value=False)
        if auto_refresh:
            st.rerun()
        
        # Manual refresh button
        if st.sidebar.button("🔄 Refresh Data"):
            st.rerun()
        
        return selected_timeframe, risk_level, account_balance
    
    def _render_market_analysis(self):
        """Render market analysis tab"""
        st.header("📊 Multi-Timeframe Market Analysis")
        
        symbol = st.session_state.selected_symbol
        
        # Generate sample data (in real implementation, fetch from exchange)
        sample_data = self._generate_sample_data()
        
        # Multi-timeframe analysis
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Price chart with indicators
            fig = self._create_price_chart(sample_data, symbol)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Timeframe signals
            st.subheader("🎯 Timeframe Signals")
            
            # Simulate MTF analysis
            timeframes = ['1h', '4h', '1d', '1w']
            for tf in timeframes:
                signal_strength = np.random.choice(['Strong Buy', 'Buy', 'Neutral', 'Sell', 'Strong Sell'])
                confidence = np.random.uniform(0.6, 0.95)
                
                color = {
                    'Strong Buy': 'green',
                    'Buy': 'lightgreen', 
                    'Neutral': 'gray',
                    'Sell': 'orange',
                    'Strong Sell': 'red'
                }[signal_strength]
                
                st.markdown(f"""
                <div style="background-color: {color}; padding: 0.5rem; margin: 0.2rem 0; border-radius: 0.3rem; color: white;">
                    <strong>{tf}:</strong> {signal_strength} ({confidence:.1%})
                </div>
                """, unsafe_allow_html=True)
        
        # Technical indicators summary
        st.subheader("📈 Technical Indicators")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            rsi = sample_data['rsi'].iloc[-1]
            rsi_signal = "Oversold" if rsi < 30 else "Overbought" if rsi > 70 else "Neutral"
            st.metric("RSI", f"{rsi:.1f}", rsi_signal)
        
        with col2:
            macd = sample_data['macd'].iloc[-1]
            macd_signal = "Bullish" if macd > 0 else "Bearish"
            st.metric("MACD", f"{macd:.2f}", macd_signal)
        
        with col3:
            bb_position = np.random.uniform(0, 1)
            bb_signal = "Upper" if bb_position > 0.8 else "Lower" if bb_position < 0.2 else "Middle"
            st.metric("BB Position", f"{bb_position:.2f}", bb_signal)
        
        with col4:
            volume_ratio = np.random.uniform(0.5, 3.0)
            volume_signal = "High" if volume_ratio > 1.5 else "Low" if volume_ratio < 0.8 else "Normal"
            st.metric("Volume Ratio", f"{volume_ratio:.2f}", volume_signal)
    
    def _render_ml_predictions(self):
        """Render ML predictions tab"""
        st.header("🤖 Machine Learning Predictions")
        
        # Model performance overview
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Ensemble Accuracy", "73.2%", "↑ 2.1%")
        
        with col2:
            st.metric("LSTM Confidence", "84.5%", "↑ 1.3%")
        
        with col3:
            st.metric("XGBoost Score", "0.68", "↓ 0.02")
        
        # Prediction results
        st.subheader("🎯 Current Predictions")
        
        # Simulate ML predictions
        models = ['LSTM', 'XGBoost', 'Random Forest', 'Linear']
        predictions_data = []
        
        for model in models:
            prediction = np.random.uniform(-0.05, 0.05)
            confidence = np.random.uniform(0.6, 0.9)
            direction = "↑ Up" if prediction > 0 else "↓ Down"
            
            predictions_data.append({
                'Model': model,
                'Prediction': f"{prediction:.2%}",
                'Direction': direction,
                'Confidence': f"{confidence:.1%}"
            })
        
        df_predictions = pd.DataFrame(predictions_data)
        st.dataframe(df_predictions, use_container_width=True)
        
        # Ensemble prediction
        ensemble_pred = np.random.uniform(-0.03, 0.03)
        ensemble_conf = np.random.uniform(0.7, 0.9)
        
        st.subheader("🎪 Ensemble Prediction")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            direction = "📈 Bullish" if ensemble_pred > 0 else "📉 Bearish"
            st.metric("Direction", direction)
        
        with col2:
            st.metric("Expected Move", f"{ensemble_pred:.2%}")
        
        with col3:
            st.metric("Confidence", f"{ensemble_conf:.1%}")
        
        # Feature importance
        st.subheader("🔍 Feature Importance")
        
        features = ['RSI', 'MACD', 'Volume', 'Price_MA_Ratio', 'Volatility']
        importance = np.random.dirichlet(np.ones(len(features)))
        
        fig_importance = px.bar(
            x=features, 
            y=importance,
            title="Model Feature Importance",
            labels={'x': 'Features', 'y': 'Importance'}
        )
        st.plotly_chart(fig_importance, use_container_width=True)
    
    def _render_risk_management(self):
        """Render risk management tab"""
        st.header("⚖️ Advanced Risk Management")
        
        # Risk metrics overview
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            portfolio_risk = np.random.uniform(0.05, 0.15)
            st.metric("Portfolio Risk", f"{portfolio_risk:.1%}", "↓ 0.2%")
        
        with col2:
            var_95 = np.random.uniform(0.02, 0.08)
            st.metric("VaR (95%)", f"{var_95:.1%}")
        
        with col3:
            sharpe_ratio = np.random.uniform(1.2, 2.5)
            st.metric("Sharpe Ratio", f"{sharpe_ratio:.2f}", "↑ 0.1")
        
        with col4:
            max_drawdown = np.random.uniform(0.08, 0.20)
            st.metric("Max Drawdown", f"{max_drawdown:.1%}")
        
        # Position sizing calculator
        st.subheader("📏 Position Sizing Calculator")
        
        col1, col2 = st.columns(2)
        
        with col1:
            entry_price = st.number_input("Entry Price ($)", value=50000.0, step=100.0)
            stop_loss = st.number_input("Stop Loss ($)", value=48000.0, step=100.0)
            account_balance = st.number_input("Account Balance ($)", value=100000.0, step=1000.0)
            risk_percent = st.slider("Risk per Trade (%)", 1, 5, 2) / 100
        
        with col2:
            # Calculate position size
            risk_amount = account_balance * risk_percent
            stop_distance = abs(entry_price - stop_loss) / entry_price
            position_size = risk_amount / stop_distance if stop_distance > 0 else 0
            
            st.metric("Risk Amount", f"${risk_amount:.2f}")
            st.metric("Stop Distance", f"{stop_distance:.2%}")
            st.metric("Position Size", f"${position_size:.2f}")
            st.metric("Quantity", f"{position_size/entry_price:.4f}")
        
        # Risk heatmap
        st.subheader("🌡️ Risk Heatmap")
        
        # Generate sample correlation matrix
        symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
        correlation_matrix = np.random.rand(len(symbols), len(symbols))
        correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
        np.fill_diagonal(correlation_matrix, 1)
        
        fig_heatmap = px.imshow(
            correlation_matrix,
            x=symbols,
            y=symbols,
            color_continuous_scale='RdYlBu_r',
            title="Asset Correlation Matrix"
        )
        st.plotly_chart(fig_heatmap, use_container_width=True)
    
    def _render_smart_alerts(self):
        """Render smart alerts tab"""
        st.header("🔔 Smart Alert System")
        
        # Alert statistics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Active Alerts", "12", "↑ 3")
        
        with col2:
            st.metric("Success Rate", "68.5%", "↑ 2.1%")
        
        with col3:
            st.metric("Avg Profit", "4.2%", "↑ 0.3%")
        
        with col4:
            st.metric("Alert Frequency", "8/day", "↓ 2")
        
        # Recent alerts
        st.subheader("📋 Recent Alerts")
        
        # Generate sample alerts
        alert_data = []
        priorities = ['🔴 Critical', '🟠 High', '🟡 Medium', '🔵 Low']
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        
        for i in range(10):
            alert_data.append({
                'Time': (datetime.now() - timedelta(hours=i)).strftime('%H:%M'),
                'Symbol': np.random.choice(symbols),
                'Priority': np.random.choice(priorities),
                'Message': f"RSI oversold condition detected",
                'Confidence': f"{np.random.uniform(0.6, 0.9):.1%}",
                'Status': np.random.choice(['✅ Sent', '⏳ Pending', '❌ Failed'])
            })
        
        df_alerts = pd.DataFrame(alert_data)
        st.dataframe(df_alerts, use_container_width=True)
        
        # Alert configuration
        st.subheader("⚙️ Alert Configuration")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.checkbox("Enable Telegram Alerts", value=True)
            st.checkbox("Enable Email Alerts", value=False)
            st.checkbox("Enable Webhook Alerts", value=False)
            
            min_confidence = st.slider("Minimum Confidence (%)", 50, 95, 70)
        
        with col2:
            cooldown_hours = st.slider("Cooldown Period (hours)", 1, 24, 4)
            max_alerts_per_day = st.slider("Max Alerts per Day", 5, 50, 20)
            
            st.selectbox("Alert Priority Filter", 
                        ['All', 'Medium+', 'High+', 'Critical Only'])
    
    def _render_backtesting(self):
        """Render backtesting tab"""
        st.header("📈 Advanced Backtesting")
        
        # Backtest configuration
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("⚙️ Backtest Settings")
            
            start_date = st.date_input("Start Date", datetime.now() - timedelta(days=365))
            end_date = st.date_input("End Date", datetime.now())
            initial_capital = st.number_input("Initial Capital ($)", value=100000, step=1000)
            commission = st.number_input("Commission (%)", value=0.1, step=0.01) / 100
        
        with col2:
            st.subheader("📊 Strategy Selection")
            
            strategy = st.selectbox("Strategy", [
                "RSI Mean Reversion",
                "MACD Crossover", 
                "Bollinger Bands",
                "ML Ensemble",
                "Multi-Timeframe"
            ])
            
            if st.button("🚀 Run Backtest"):
                # Simulate backtest results
                with st.spinner("Running backtest..."):
                    import time
                    time.sleep(2)  # Simulate processing
                    
                    # Generate sample results
                    total_return = np.random.uniform(0.1, 0.5)
                    sharpe_ratio = np.random.uniform(1.0, 2.5)
                    max_drawdown = np.random.uniform(0.05, 0.20)
                    win_rate = np.random.uniform(0.55, 0.75)
                    
                    st.success("Backtest completed!")
        
        # Backtest results
        st.subheader("📊 Backtest Results")
        
        # Performance metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Return", "34.2%")
        
        with col2:
            st.metric("Sharpe Ratio", "1.85")
        
        with col3:
            st.metric("Max Drawdown", "12.3%")
        
        with col4:
            st.metric("Win Rate", "64.5%")
        
        # Equity curve
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        equity_curve = 100000 * (1 + np.cumsum(np.random.randn(len(dates)) * 0.01))
        
        fig_equity = go.Figure()
        fig_equity.add_trace(go.Scatter(
            x=dates, 
            y=equity_curve,
            mode='lines',
            name='Equity Curve',
            line=dict(color='blue', width=2)
        ))
        
        fig_equity.update_layout(
            title="Portfolio Equity Curve",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            hovermode='x unified'
        )
        
        st.plotly_chart(fig_equity, use_container_width=True)
    
    def _render_portfolio(self):
        """Render portfolio tab"""
        st.header("💼 Portfolio Management")
        
        # Portfolio overview
        portfolio_value = 125000
        daily_pnl = 2500
        daily_pnl_pct = daily_pnl / portfolio_value
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Portfolio Value", f"${portfolio_value:,.2f}", f"${daily_pnl:,.2f}")
        
        with col2:
            st.metric("Daily P&L", f"{daily_pnl_pct:.2%}", "↑ 0.5%")
        
        with col3:
            st.metric("Total Positions", "8", "↑ 2")
        
        with col4:
            st.metric("Available Cash", "$25,000", "↓ $5,000")
        
        # Portfolio allocation
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🥧 Asset Allocation")
            
            allocation_data = {
                'Asset': ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'Cash'],
                'Allocation': [35, 25, 15, 10, 10, 5],
                'Value': [43750, 31250, 18750, 12500, 12500, 6250]
            }
            
            fig_pie = px.pie(
                values=allocation_data['Allocation'],
                names=allocation_data['Asset'],
                title="Portfolio Allocation"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            st.subheader("📊 Position Details")
            
            position_data = pd.DataFrame({
                'Symbol': ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT'],
                'Quantity': [0.875, 12.5, 15000, 1250],
                'Avg Price': [50000, 2500, 1.25, 10],
                'Current Price': [51000, 2600, 1.30, 9.8],
                'P&L': [875, 1250, 750, -250],
                'P&L %': [2.0, 4.0, 4.0, -2.0]
            })
            
            st.dataframe(position_data, use_container_width=True)
        
        # Performance chart
        st.subheader("📈 Portfolio Performance")
        
        # Generate sample performance data
        dates = pd.date_range(start='2023-01-01', periods=365, freq='D')
        performance = np.cumsum(np.random.randn(365) * 0.01) + 0.25
        
        fig_performance = go.Figure()
        fig_performance.add_trace(go.Scatter(
            x=dates,
            y=performance,
            mode='lines',
            name='Portfolio Return',
            line=dict(color='green', width=2)
        ))
        
        # Add benchmark
        benchmark = np.cumsum(np.random.randn(365) * 0.008) + 0.15
        fig_performance.add_trace(go.Scatter(
            x=dates,
            y=benchmark,
            mode='lines',
            name='Benchmark (BTC)',
            line=dict(color='orange', width=2, dash='dash')
        ))
        
        fig_performance.update_layout(
            title="Portfolio vs Benchmark Performance",
            xaxis_title="Date",
            yaxis_title="Cumulative Return",
            hovermode='x unified'
        )
        
        st.plotly_chart(fig_performance, use_container_width=True)
    
    def _generate_sample_data(self, periods: int = 100) -> pd.DataFrame:
        """Generate sample OHLCV data with indicators"""
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='4H')
        
        # Generate price data
        np.random.seed(42)
        close_prices = 50000 + np.cumsum(np.random.randn(periods) * 100)
        
        data = pd.DataFrame({
            'open': close_prices + np.random.randn(periods) * 50,
            'high': close_prices + np.abs(np.random.randn(periods) * 100),
            'low': close_prices - np.abs(np.random.randn(periods) * 100),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, periods)
        }, index=dates)
        
        # Add technical indicators
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = data['close'].ewm(span=12).mean()
        exp2 = data['close'].ewm(span=26).mean()
        data['macd'] = exp1 - exp2
        data['macd_signal'] = data['macd'].ewm(span=9).mean()
        
        return data
    
    def _create_price_chart(self, data: pd.DataFrame, symbol: str) -> go.Figure:
        """Create interactive price chart with indicators"""
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=(f'{symbol} Price', 'RSI', 'MACD'),
            row_heights=[0.6, 0.2, 0.2]
        )
        
        # Candlestick chart
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close'],
                name='Price'
            ),
            row=1, col=1
        )
        
        # RSI
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data['rsi'],
                mode='lines',
                name='RSI',
                line=dict(color='purple')
            ),
            row=2, col=1
        )
        
        # RSI levels
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=2, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=2, col=1)
        
        # MACD
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data['macd'],
                mode='lines',
                name='MACD',
                line=dict(color='blue')
            ),
            row=3, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data['macd_signal'],
                mode='lines',
                name='Signal',
                line=dict(color='red')
            ),
            row=3, col=1
        )
        
        fig.update_layout(
            title=f"{symbol} Technical Analysis",
            xaxis_rangeslider_visible=False,
            height=800
        )
        
        return fig
    
    def _initialize_portfolio(self) -> Dict:
        """Initialize sample portfolio data"""
        return {
            'total_value': 125000,
            'cash': 25000,
            'positions': {
                'BTC/USDT': {'quantity': 0.875, 'avg_price': 50000},
                'ETH/USDT': {'quantity': 12.5, 'avg_price': 2500},
                'ADA/USDT': {'quantity': 15000, 'avg_price': 1.25},
                'DOT/USDT': {'quantity': 1250, 'avg_price': 10}
            }
        }

# Main execution
if __name__ == "__main__":
    dashboard = TradingDashboard()
    dashboard.run()

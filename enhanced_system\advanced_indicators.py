"""
Advanced Technical Indicators for Enhanced Crypto Trading System
Includes Bollinger Bands, Fibonacci levels, Volume Profile, and more
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FibonacciLevels:
    """Fibonacci retracement levels"""
    level_0: float
    level_236: float
    level_382: float
    level_500: float
    level_618: float
    level_786: float
    level_100: float

class AdvancedIndicators:
    """Advanced technical indicators calculator"""
    
    @staticmethod
    def bollinger_bands(df: pd.DataFrame, 
                       period: int = 20, 
                       std_dev: float = 2.0) -> pd.DataFrame:
        """
        Calculate Bollinger Bands
        
        Args:
            df: DataFrame with OHLCV data
            period: Moving average period
            std_dev: Standard deviation multiplier
            
        Returns:
            DataFrame with Bollinger Bands
        """
        try:
            df = df.copy()
            
            # Calculate middle band (SMA)
            df['bb_middle'] = df['close'].rolling(window=period).mean()
            
            # Calculate standard deviation
            rolling_std = df['close'].rolling(window=period).std()
            
            # Calculate upper and lower bands
            df['bb_upper'] = df['bb_middle'] + (rolling_std * std_dev)
            df['bb_lower'] = df['bb_middle'] - (rolling_std * std_dev)
            
            # Calculate band width and position
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Bollinger Band squeeze detection
            df['bb_squeeze'] = df['bb_width'] < df['bb_width'].rolling(20).mean() * 0.8
            
            logger.info("Bollinger Bands calculated successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return df
    
    @staticmethod
    def fibonacci_retracement(high_price: float, 
                            low_price: float, 
                            trend_direction: str = "up") -> FibonacciLevels:
        """
        Calculate Fibonacci retracement levels
        
        Args:
            high_price: Highest price in the range
            low_price: Lowest price in the range
            trend_direction: "up" or "down"
            
        Returns:
            FibonacciLevels object
        """
        try:
            price_range = high_price - low_price
            
            if trend_direction.lower() == "up":
                # Uptrend retracement (from high)
                levels = FibonacciLevels(
                    level_0=high_price,
                    level_236=high_price - (price_range * 0.236),
                    level_382=high_price - (price_range * 0.382),
                    level_500=high_price - (price_range * 0.500),
                    level_618=high_price - (price_range * 0.618),
                    level_786=high_price - (price_range * 0.786),
                    level_100=low_price
                )
            else:
                # Downtrend retracement (from low)
                levels = FibonacciLevels(
                    level_0=low_price,
                    level_236=low_price + (price_range * 0.236),
                    level_382=low_price + (price_range * 0.382),
                    level_500=low_price + (price_range * 0.500),
                    level_618=low_price + (price_range * 0.618),
                    level_786=low_price + (price_range * 0.786),
                    level_100=high_price
                )
            
            logger.info(f"Fibonacci levels calculated for {trend_direction} trend")
            return levels
            
        except Exception as e:
            logger.error(f"Error calculating Fibonacci levels: {e}")
            return None
    
    @staticmethod
    def volume_profile(df: pd.DataFrame, 
                      bins: int = 50) -> Dict[str, float]:
        """
        Calculate Volume Profile (simplified)
        
        Args:
            df: DataFrame with OHLCV data
            bins: Number of price bins
            
        Returns:
            Dictionary with volume profile data
        """
        try:
            # Create price bins
            price_min = df['low'].min()
            price_max = df['high'].max()
            price_bins = np.linspace(price_min, price_max, bins)
            
            # Calculate volume for each price level
            volume_profile = {}
            total_volume = 0
            
            for i in range(len(price_bins) - 1):
                bin_low = price_bins[i]
                bin_high = price_bins[i + 1]
                bin_center = (bin_low + bin_high) / 2
                
                # Find candles that overlap with this price bin
                overlapping_candles = df[
                    (df['low'] <= bin_high) & (df['high'] >= bin_low)
                ]
                
                # Calculate volume for this bin
                bin_volume = overlapping_candles['volume'].sum()
                volume_profile[bin_center] = bin_volume
                total_volume += bin_volume
            
            # Find Point of Control (POC) - price level with highest volume
            poc_price = max(volume_profile, key=volume_profile.get)
            poc_volume = volume_profile[poc_price]
            
            # Calculate Value Area (70% of volume)
            sorted_levels = sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)
            value_area_volume = 0
            value_area_high = 0
            value_area_low = float('inf')
            
            for price, volume in sorted_levels:
                value_area_volume += volume
                value_area_high = max(value_area_high, price)
                value_area_low = min(value_area_low, price)
                
                if value_area_volume >= total_volume * 0.7:
                    break
            
            result = {
                'poc_price': poc_price,
                'poc_volume': poc_volume,
                'value_area_high': value_area_high,
                'value_area_low': value_area_low,
                'total_volume': total_volume
            }
            
            logger.info("Volume Profile calculated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating Volume Profile: {e}")
            return {}
    
    @staticmethod
    def ichimoku_cloud(df: pd.DataFrame,
                      tenkan_period: int = 9,
                      kijun_period: int = 26,
                      senkou_period: int = 52) -> pd.DataFrame:
        """
        Calculate Ichimoku Cloud indicators
        
        Args:
            df: DataFrame with OHLCV data
            tenkan_period: Tenkan-sen period
            kijun_period: Kijun-sen period
            senkou_period: Senkou Span B period
            
        Returns:
            DataFrame with Ichimoku indicators
        """
        try:
            df = df.copy()
            
            # Tenkan-sen (Conversion Line)
            tenkan_high = df['high'].rolling(window=tenkan_period).max()
            tenkan_low = df['low'].rolling(window=tenkan_period).min()
            df['tenkan_sen'] = (tenkan_high + tenkan_low) / 2
            
            # Kijun-sen (Base Line)
            kijun_high = df['high'].rolling(window=kijun_period).max()
            kijun_low = df['low'].rolling(window=kijun_period).min()
            df['kijun_sen'] = (kijun_high + kijun_low) / 2
            
            # Senkou Span A (Leading Span A)
            df['senkou_span_a'] = ((df['tenkan_sen'] + df['kijun_sen']) / 2).shift(kijun_period)
            
            # Senkou Span B (Leading Span B)
            senkou_high = df['high'].rolling(window=senkou_period).max()
            senkou_low = df['low'].rolling(window=senkou_period).min()
            df['senkou_span_b'] = ((senkou_high + senkou_low) / 2).shift(kijun_period)
            
            # Chikou Span (Lagging Span)
            df['chikou_span'] = df['close'].shift(-kijun_period)
            
            # Cloud analysis
            df['cloud_green'] = df['senkou_span_a'] > df['senkou_span_b']
            df['price_above_cloud'] = df['close'] > np.maximum(df['senkou_span_a'], df['senkou_span_b'])
            df['price_below_cloud'] = df['close'] < np.minimum(df['senkou_span_a'], df['senkou_span_b'])
            
            logger.info("Ichimoku Cloud calculated successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating Ichimoku Cloud: {e}")
            return df
    
    @staticmethod
    def support_resistance_levels(df: pd.DataFrame, 
                                 window: int = 20,
                                 min_touches: int = 2) -> Dict[str, List[float]]:
        """
        Identify support and resistance levels
        
        Args:
            df: DataFrame with OHLCV data
            window: Window for local extrema detection
            min_touches: Minimum touches to confirm level
            
        Returns:
            Dictionary with support and resistance levels
        """
        try:
            # Find local highs and lows
            highs = df['high'].rolling(window=window, center=True).max() == df['high']
            lows = df['low'].rolling(window=window, center=True).min() == df['low']
            
            # Extract significant levels
            resistance_candidates = df.loc[highs, 'high'].tolist()
            support_candidates = df.loc[lows, 'low'].tolist()
            
            # Group similar levels and count touches
            def group_levels(levels, tolerance=0.01):
                grouped = []
                for level in levels:
                    added = False
                    for group in grouped:
                        if abs(level - group['price']) / group['price'] <= tolerance:
                            group['touches'] += 1
                            group['price'] = (group['price'] + level) / 2  # Average
                            added = True
                            break
                    if not added:
                        grouped.append({'price': level, 'touches': 1})
                return grouped
            
            resistance_groups = group_levels(resistance_candidates)
            support_groups = group_levels(support_candidates)
            
            # Filter by minimum touches
            resistance_levels = [g['price'] for g in resistance_groups if g['touches'] >= min_touches]
            support_levels = [g['price'] for g in support_groups if g['touches'] >= min_touches]
            
            result = {
                'resistance': sorted(resistance_levels, reverse=True),
                'support': sorted(support_levels)
            }
            
            logger.info(f"Found {len(resistance_levels)} resistance and {len(support_levels)} support levels")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance levels: {e}")
            return {'resistance': [], 'support': []}
    
    @staticmethod
    def market_structure_analysis(df: pd.DataFrame) -> Dict[str, any]:
        """
        Analyze market structure (Higher Highs, Lower Lows, etc.)
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dictionary with market structure analysis
        """
        try:
            # Find swing highs and lows
            window = 10
            swing_highs = df['high'].rolling(window=window, center=True).max() == df['high']
            swing_lows = df['low'].rolling(window=window, center=True).min() == df['low']
            
            # Extract swing points
            highs_data = df.loc[swing_highs, ['high']].copy()
            lows_data = df.loc[swing_lows, ['low']].copy()
            
            # Analyze trend structure
            higher_highs = 0
            lower_highs = 0
            higher_lows = 0
            lower_lows = 0
            
            # Count higher/lower highs
            for i in range(1, len(highs_data)):
                if highs_data.iloc[i]['high'] > highs_data.iloc[i-1]['high']:
                    higher_highs += 1
                else:
                    lower_highs += 1
            
            # Count higher/lower lows
            for i in range(1, len(lows_data)):
                if lows_data.iloc[i]['low'] > lows_data.iloc[i-1]['low']:
                    higher_lows += 1
                else:
                    lower_lows += 1
            
            # Determine trend
            if higher_highs > lower_highs and higher_lows > lower_lows:
                trend = "uptrend"
            elif lower_highs > higher_highs and lower_lows > higher_lows:
                trend = "downtrend"
            else:
                trend = "sideways"
            
            result = {
                'trend': trend,
                'higher_highs': higher_highs,
                'lower_highs': lower_highs,
                'higher_lows': higher_lows,
                'lower_lows': lower_lows,
                'trend_strength': abs(higher_highs - lower_highs) + abs(higher_lows - lower_lows)
            }
            
            logger.info(f"Market structure: {trend} (strength: {result['trend_strength']})")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing market structure: {e}")
            return {'trend': 'unknown', 'trend_strength': 0}

# Example usage
if __name__ == "__main__":
    # Create sample data for testing
    dates = pd.date_range('2023-01-01', periods=100, freq='4H')
    np.random.seed(42)
    
    # Generate sample OHLCV data
    close_prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    sample_data = pd.DataFrame({
        'timestamp': dates,
        'open': close_prices + np.random.randn(100) * 50,
        'high': close_prices + np.abs(np.random.randn(100) * 100),
        'low': close_prices - np.abs(np.random.randn(100) * 100),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    # Test indicators
    indicators = AdvancedIndicators()
    
    # Test Bollinger Bands
    bb_data = indicators.bollinger_bands(sample_data)
    print("Bollinger Bands calculated")
    
    # Test Fibonacci levels
    fib_levels = indicators.fibonacci_retracement(52000, 48000, "up")
    print(f"Fibonacci 50% level: {fib_levels.level_500}")
    
    # Test Volume Profile
    volume_profile = indicators.volume_profile(sample_data)
    print(f"POC Price: {volume_profile.get('poc_price', 'N/A')}")
    
    # Test Market Structure
    market_structure = indicators.market_structure_analysis(sample_data)
    print(f"Market Trend: {market_structure['trend']}")

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sklearn.metrics import accuracy_score
import ta
from rich.progress import track

# Load configuration
with open('config/settings.json', 'r') as f:
    CONFIG = json.load(f)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/signal_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SignalAnalyzer:
    def __init__(self):
        """Initialize the signal analyzer"""
        self.data_dir = Path('data')
        self.results_file = 'config/signal_analysis.json'
        self.min_success_rate = CONFIG['signal_analysis']['min_success_rate']
        self.min_profit_target = CONFIG['signal_analysis']['min_profit_target']
        self.min_sample_size = CONFIG['signal_analysis']['min_sample_size']

    def analyze_indicator_combination(
        self,
        df: pd.DataFrame,
        combination: Dict[str, Dict]
    ) -> Tuple[float, float, float]:
        """Analyze a specific combination of indicators"""
        try:
            signals = []
            returns = []
            holding_periods = []
            
            for i in range(len(df) - 30):  # Leave 30 periods for forward testing
                # Check if combination criteria are met
                signal = True
                
                # RSI condition
                if 'rsi' in combination:
                    rsi = df['rsi'].iloc[i]
                    if not (rsi < combination['rsi']['threshold']):
                        signal = False
                
                # MACD condition
                if 'macd' in combination:
                    macd = df['macd'].iloc[i]
                    macd_signal = df['macd_signal'].iloc[i]
                    if not (macd > macd_signal and macd < 0):
                        signal = False
                
                # ADX condition
                if 'adx' in combination:
                    adx = df['adx'].iloc[i]
                    if not (adx > combination['adx']['threshold']):
                        signal = False
                
                # OBV condition
                if 'obv' in combination:
                    obv = df['obv'].iloc[i]
                    obv_sma = df['obv'].rolling(20).mean().iloc[i]
                    if not (obv > obv_sma):
                        signal = False
                
                # CMF condition
                if 'cmf' in combination:
                    cmf = df['cmf'].iloc[i]
                    if not (cmf > combination['cmf']['threshold']):
                        signal = False
                
                if signal:
                    # Calculate forward returns
                    entry_price = df['close'].iloc[i]
                    future_prices = df['close'].iloc[i+1:i+31]  # Look forward 30 periods
                    
                    max_return = ((future_prices.max() - entry_price) / entry_price)
                    periods_to_target = future_prices[
                        future_prices >= entry_price * (1 + self.min_profit_target)
                    ].index.min()
                    
                    if max_return >= self.min_profit_target:
                        signals.append(1)
                        returns.append(max_return)
                        if periods_to_target is not None:
                            holding_periods.append(
                                (periods_to_target - df.index[i]).total_seconds() / 3600
                            )
                    else:
                        signals.append(0)
                        returns.append(max_return)
                        holding_periods.append(30 * 4)  # 30 periods * 4 hours
            
            if len(signals) < self.min_sample_size:
                return 0, 0, 0
                
            success_rate = np.mean(signals)
            avg_return = np.mean(returns)
            avg_holding_period = np.mean(holding_periods)
            
            return success_rate, avg_return, avg_holding_period
            
        except Exception as e:
            logger.error(f"Error analyzing combination: {e}")
            return 0, 0, 0

    def analyze_all_combinations(self) -> Dict:
        """Analyze all possible indicator combinations"""
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'parameters': {
                'min_success_rate': self.min_success_rate,
                'min_profit_target': self.min_profit_target,
                'min_sample_size': self.min_sample_size
            },
            'combinations': []
        }
        
        # Define base combinations to test
        base_combinations = [
            {'rsi': {'threshold': 30}},
            {'macd': {'threshold': 0}},
            {'adx': {'threshold': 25}},
            {'obv': {'threshold': 0}},
            {'cmf': {'threshold': 0.1}}
        ]
        
        # Generate all possible combinations
        from itertools import combinations
        all_combinations = []
        for r in range(2, len(base_combinations) + 1):
            for combo in combinations(base_combinations, r):
                combined = {}
                for d in combo:
                    combined.update(d)
                all_combinations.append(combined)
        
        # Analyze each symbol
        for file in track(list(self.data_dir.glob('*.csv')), description="Analyzing symbols"):
            try:
                symbol = file.stem
                df = pd.read_csv(file)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
                
                # Analyze each combination
                for combo in all_combinations:
                    success_rate, avg_return, avg_holding_period = self.analyze_indicator_combination(df, combo)
                    
                    if success_rate >= self.min_success_rate:
                        results['combinations'].append({
                            'symbol': symbol,
                            'indicators': combo,
                            'success_rate': success_rate,
                            'average_return': avg_return,
                            'average_holding_period': avg_holding_period
                        })
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        # Save results
        with open(self.results_file, 'w') as f:
            json.dump(results, f, indent=4)
        
        logger.info(f"Analysis completed. Found {len(results['combinations'])} successful combinations.")
        return results

def main():
    """Main entry point"""
    try:
        analyzer = SignalAnalyzer()
        analyzer.analyze_all_combinations()
    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    main()

# Enhanced Crypto Trading System Requirements

# Core Dependencies
ccxt>=4.0.0
pandas>=2.0.0
numpy>=1.24.0
ta>=0.10.2
python-telegram-bot>=20.0
python-dotenv>=1.0.0

# Machine Learning
scikit-learn>=1.3.0
tensorflow>=2.13.0
xgboost>=1.7.0

# Data Visualization & UI
streamlit>=1.28.0
plotly>=5.15.0
rich>=13.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Database & Caching
redis>=4.6.0
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
alembic>=1.12.0

# API & Web Framework
fastapi>=0.100.0
uvicorn>=0.23.0
aiohttp>=3.8.0
aioredis>=2.0.0

# Additional Technical Analysis
TA-Lib>=0.4.25

# Utilities
requests>=2.31.0
python-dateutil>=2.8.2
pytz>=2023.3
asyncio>=3.4.3
concurrent-futures>=3.1.1

# System Monitoring
psutil>=5.9.0

# Data Processing
scipy>=1.11.0
statsmodels>=0.14.0

# Configuration & Logging
pyyaml>=6.0
colorlog>=6.7.0

# Testing (Optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Development Tools (Optional)
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: For advanced features
# prophet>=1.1.4  # Facebook Prophet for time series forecasting
# neuralprophet>=0.6.0  # Neural Prophet
# optuna>=3.3.0  # Hyperparameter optimization
# shap>=0.42.0  # Model explainability

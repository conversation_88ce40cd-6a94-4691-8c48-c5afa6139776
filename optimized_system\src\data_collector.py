import os
import json
import ccxt
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional
import ta
from rich.progress import track

# Load configuration
with open('config/settings.json', 'r') as f:
    CONFIG = json.load(f)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataCollector:
    def __init__(self):
        """Initialize the data collector"""
        self.exchange = ccxt.binance()
        self.timeframe = CONFIG['timeframe']
        self.limit = CONFIG['candles_limit']
        self.data_dir = Path('data')
        self.data_dir.mkdir(exist_ok=True)

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for a DataFrame"""
        try:
            # RSI
            df['rsi'] = ta.momentum.RSIIndicator(
                df['close'], 
                window=CONFIG['indicators']['rsi']['period']
            ).rsi()

            # MACD
            macd = ta.trend.MACD(
                df['close'],
                window_fast=CONFIG['indicators']['macd']['fast_period'],
                window_slow=CONFIG['indicators']['macd']['slow_period'],
                window_sign=CONFIG['indicators']['macd']['signal_period']
            )
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()

            # OBV
            df['obv'] = ta.volume.OnBalanceVolumeIndicator(
                df['close'],
                df['volume']
            ).on_balance_volume()

            # ADX
            adx = ta.trend.ADXIndicator(
                df['high'],
                df['low'],
                df['close'],
                window=CONFIG['indicators']['adx']['period']
            )
            df['adx'] = adx.adx()
            df['adx_pos'] = adx.adx_pos()
            df['adx_neg'] = adx.adx_neg()

            # CMF
            df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
                df['high'],
                df['low'],
                df['close'],
                df['volume'],
                window=CONFIG['indicators']['cmf']['period']
            ).chaikin_money_flow()

            return df

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return None

    def fetch_symbol_data(self, symbol: str) -> bool:
        """Fetch and process data for a single symbol"""
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(
                symbol,
                timeframe=self.timeframe,
                limit=self.limit
            )

            if not ohlcv or len(ohlcv) < self.limit:
                logger.warning(f"Insufficient data for {symbol}")
                return False

            # Convert to DataFrame
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Calculate indicators
            df = self.calculate_indicators(df)
            if df is None:
                return False

            # Save to file
            file_path = self.data_dir / f"{symbol.replace('/', '')}.csv"
            df.to_csv(file_path, index=True)
            logger.info(f"Successfully processed {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            return False

    def collect_data(self, symbols: List[str]) -> None:
        """Collect data for all symbols"""
        logger.info(f"Starting data collection for {len(symbols)} symbols")
        
        with ThreadPoolExecutor(max_workers=CONFIG['concurrent_downloads']) as executor:
            results = list(track(
                executor.map(self.fetch_symbol_data, symbols),
                description="Collecting data",
                total=len(symbols)
            ))

        success_count = sum(results)
        logger.info(f"Data collection completed. Success: {success_count}/{len(symbols)}")

def main():
    """Main entry point"""
    try:
        # Load symbols
        with open('config/symbols.txt', 'r') as f:
            symbols = [line.strip() for line in f if line.strip() and not line.startswith('#')]

        if not symbols:
            logger.error("No symbols found in symbols.txt")
            return

        # Create and run collector
        collector = DataCollector()
        collector.collect_data(symbols)

    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    main()

"""
Multi-Timeframe Analysis System for Enhanced Crypto Trading
Analyzes multiple timeframes to provide comprehensive market view
"""

import ccxt
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimeframeSignal(Enum):
    """Signal strength enumeration"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    NEUTRAL = "neutral"
    SELL = "sell"
    STRONG_SELL = "strong_sell"

@dataclass
class TimeframeAnalysis:
    """Analysis result for a specific timeframe"""
    timeframe: str
    trend_direction: str
    trend_strength: float
    signal: TimeframeSignal
    rsi: float
    macd_signal: str
    volume_trend: str
    support_level: float
    resistance_level: float
    confidence: float

class MultiTimeframeAnalyzer:
    """Multi-timeframe analysis system"""
    
    def __init__(self, exchange_id: str = 'binance'):
        """Initialize the analyzer"""
        self.exchange = getattr(ccxt, exchange_id)()
        self.timeframes = ['1h', '4h', '1d', '1w']
        self.timeframe_weights = {
            '1h': 0.15,   # Short-term
            '4h': 0.25,   # Medium-term
            '1d': 0.35,   # Long-term
            '1w': 0.25    # Very long-term
        }
        
    def analyze_symbol(self, symbol: str, limit: int = 100) -> Dict[str, TimeframeAnalysis]:
        """
        Analyze a symbol across multiple timeframes
        
        Args:
            symbol: Trading symbol (e.g., 'BTC/USDT')
            limit: Number of candles to fetch
            
        Returns:
            Dictionary with analysis for each timeframe
        """
        try:
            results = {}
            
            # Analyze each timeframe
            for timeframe in self.timeframes:
                logger.info(f"Analyzing {symbol} on {timeframe} timeframe")
                
                # Fetch data
                data = self._fetch_ohlcv(symbol, timeframe, limit)
                if data is None:
                    continue
                
                # Perform analysis
                analysis = self._analyze_timeframe(data, timeframe)
                results[timeframe] = analysis
                
                # Add small delay to respect rate limits
                time.sleep(0.1)
            
            logger.info(f"Multi-timeframe analysis completed for {symbol}")
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return {}
    
    def get_overall_signal(self, analyses: Dict[str, TimeframeAnalysis]) -> Dict[str, any]:
        """
        Calculate overall signal from multiple timeframe analyses
        
        Args:
            analyses: Dictionary of timeframe analyses
            
        Returns:
            Overall signal and confidence
        """
        try:
            if not analyses:
                return {'signal': TimeframeSignal.NEUTRAL, 'confidence': 0.0}
            
            # Calculate weighted signal score
            total_score = 0
            total_weight = 0
            trend_alignment = 0
            
            signal_scores = {
                TimeframeSignal.STRONG_SELL: -2,
                TimeframeSignal.SELL: -1,
                TimeframeSignal.NEUTRAL: 0,
                TimeframeSignal.BUY: 1,
                TimeframeSignal.STRONG_BUY: 2
            }
            
            for timeframe, analysis in analyses.items():
                weight = self.timeframe_weights.get(timeframe, 0.25)
                score = signal_scores.get(analysis.signal, 0)
                
                total_score += score * weight * analysis.confidence
                total_weight += weight
                
                # Check trend alignment
                if analysis.trend_direction in ['uptrend', 'strong_uptrend']:
                    trend_alignment += weight
                elif analysis.trend_direction in ['downtrend', 'strong_downtrend']:
                    trend_alignment -= weight
            
            # Calculate final signal
            if total_weight > 0:
                weighted_score = total_score / total_weight
            else:
                weighted_score = 0
            
            # Determine overall signal
            if weighted_score >= 1.5:
                overall_signal = TimeframeSignal.STRONG_BUY
            elif weighted_score >= 0.5:
                overall_signal = TimeframeSignal.BUY
            elif weighted_score <= -1.5:
                overall_signal = TimeframeSignal.STRONG_SELL
            elif weighted_score <= -0.5:
                overall_signal = TimeframeSignal.SELL
            else:
                overall_signal = TimeframeSignal.NEUTRAL
            
            # Calculate confidence based on trend alignment
            trend_alignment_score = abs(trend_alignment) / sum(self.timeframe_weights.values())
            confidence = min(trend_alignment_score, 1.0)
            
            result = {
                'signal': overall_signal,
                'confidence': confidence,
                'weighted_score': weighted_score,
                'trend_alignment': trend_alignment_score,
                'timeframe_count': len(analyses)
            }
            
            logger.info(f"Overall signal: {overall_signal.value} (confidence: {confidence:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating overall signal: {e}")
            return {'signal': TimeframeSignal.NEUTRAL, 'confidence': 0.0}
    
    def _fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> Optional[pd.DataFrame]:
        """Fetch OHLCV data for a symbol and timeframe"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            if not ohlcv:
                logger.warning(f"No data received for {symbol} {timeframe}")
                return None
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol} {timeframe}: {e}")
            return None
    
    def _analyze_timeframe(self, df: pd.DataFrame, timeframe: str) -> TimeframeAnalysis:
        """Analyze a single timeframe"""
        try:
            # Calculate indicators
            df = self._calculate_indicators(df)
            
            # Analyze trend
            trend_direction, trend_strength = self._analyze_trend(df)
            
            # Calculate RSI signal
            rsi = df['rsi'].iloc[-1]
            
            # Calculate MACD signal
            macd_signal = self._get_macd_signal(df)
            
            # Analyze volume
            volume_trend = self._analyze_volume_trend(df)
            
            # Find support and resistance
            support_level, resistance_level = self._find_support_resistance(df)
            
            # Determine overall signal
            signal = self._determine_signal(df, trend_direction, trend_strength)
            
            # Calculate confidence
            confidence = self._calculate_confidence(df, trend_strength, timeframe)
            
            analysis = TimeframeAnalysis(
                timeframe=timeframe,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                signal=signal,
                rsi=rsi,
                macd_signal=macd_signal,
                volume_trend=volume_trend,
                support_level=support_level,
                resistance_level=resistance_level,
                confidence=confidence
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe {timeframe}: {e}")
            return TimeframeAnalysis(
                timeframe=timeframe,
                trend_direction="unknown",
                trend_strength=0.0,
                signal=TimeframeSignal.NEUTRAL,
                rsi=50.0,
                macd_signal="neutral",
                volume_trend="neutral",
                support_level=0.0,
                resistance_level=0.0,
                confidence=0.0
            )
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Moving averages
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['ema_20'] = df['close'].ewm(span=20).mean()
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(20).mean()
        
        return df
    
    def _analyze_trend(self, df: pd.DataFrame) -> Tuple[str, float]:
        """Analyze trend direction and strength"""
        try:
            # Price vs moving averages
            current_price = df['close'].iloc[-1]
            sma_20 = df['sma_20'].iloc[-1]
            sma_50 = df['sma_50'].iloc[-1]
            
            # Moving average alignment
            ma_score = 0
            if current_price > sma_20:
                ma_score += 1
            if current_price > sma_50:
                ma_score += 1
            if sma_20 > sma_50:
                ma_score += 1
            
            # Price momentum
            price_change_20 = (current_price - df['close'].iloc[-21]) / df['close'].iloc[-21]
            
            # Determine trend
            if ma_score >= 2 and price_change_20 > 0.05:
                trend = "strong_uptrend"
                strength = 0.8 + min(price_change_20 * 2, 0.2)
            elif ma_score >= 2:
                trend = "uptrend"
                strength = 0.6 + min(abs(price_change_20), 0.2)
            elif ma_score <= 1 and price_change_20 < -0.05:
                trend = "strong_downtrend"
                strength = 0.8 + min(abs(price_change_20) * 2, 0.2)
            elif ma_score <= 1:
                trend = "downtrend"
                strength = 0.6 + min(abs(price_change_20), 0.2)
            else:
                trend = "sideways"
                strength = 0.3
            
            return trend, min(strength, 1.0)
            
        except Exception as e:
            logger.error(f"Error analyzing trend: {e}")
            return "unknown", 0.0
    
    def _get_macd_signal(self, df: pd.DataFrame) -> str:
        """Get MACD signal"""
        try:
            macd = df['macd'].iloc[-1]
            macd_signal = df['macd_signal'].iloc[-1]
            macd_prev = df['macd'].iloc[-2]
            signal_prev = df['macd_signal'].iloc[-2]
            
            # Check for crossovers
            if macd > macd_signal and macd_prev <= signal_prev:
                return "bullish_crossover"
            elif macd < macd_signal and macd_prev >= signal_prev:
                return "bearish_crossover"
            elif macd > macd_signal:
                return "bullish"
            elif macd < macd_signal:
                return "bearish"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"Error getting MACD signal: {e}")
            return "neutral"
    
    def _analyze_volume_trend(self, df: pd.DataFrame) -> str:
        """Analyze volume trend"""
        try:
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume_sma'].iloc[-1]
            
            if current_volume > avg_volume * 1.5:
                return "high"
            elif current_volume > avg_volume * 1.2:
                return "above_average"
            elif current_volume < avg_volume * 0.8:
                return "low"
            else:
                return "average"
                
        except Exception as e:
            logger.error(f"Error analyzing volume trend: {e}")
            return "unknown"
    
    def _find_support_resistance(self, df: pd.DataFrame) -> Tuple[float, float]:
        """Find nearest support and resistance levels"""
        try:
            current_price = df['close'].iloc[-1]
            
            # Simple support/resistance based on recent highs and lows
            recent_data = df.tail(50)
            
            # Find resistance (recent highs above current price)
            resistance_candidates = recent_data[recent_data['high'] > current_price]['high']
            resistance = resistance_candidates.min() if not resistance_candidates.empty else current_price * 1.05
            
            # Find support (recent lows below current price)
            support_candidates = recent_data[recent_data['low'] < current_price]['low']
            support = support_candidates.max() if not support_candidates.empty else current_price * 0.95
            
            return support, resistance
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")
            return 0.0, 0.0
    
    def _determine_signal(self, df: pd.DataFrame, trend_direction: str, trend_strength: float) -> TimeframeSignal:
        """Determine trading signal"""
        try:
            rsi = df['rsi'].iloc[-1]
            macd_signal = self._get_macd_signal(df)
            
            # Signal scoring
            signal_score = 0
            
            # Trend component
            if trend_direction in ['strong_uptrend', 'uptrend']:
                signal_score += 2 if 'strong' in trend_direction else 1
            elif trend_direction in ['strong_downtrend', 'downtrend']:
                signal_score -= 2 if 'strong' in trend_direction else 1
            
            # RSI component
            if rsi < 30:
                signal_score += 1  # Oversold
            elif rsi > 70:
                signal_score -= 1  # Overbought
            
            # MACD component
            if macd_signal == "bullish_crossover":
                signal_score += 2
            elif macd_signal == "bearish_crossover":
                signal_score -= 2
            elif macd_signal == "bullish":
                signal_score += 1
            elif macd_signal == "bearish":
                signal_score -= 1
            
            # Determine final signal
            if signal_score >= 3:
                return TimeframeSignal.STRONG_BUY
            elif signal_score >= 1:
                return TimeframeSignal.BUY
            elif signal_score <= -3:
                return TimeframeSignal.STRONG_SELL
            elif signal_score <= -1:
                return TimeframeSignal.SELL
            else:
                return TimeframeSignal.NEUTRAL
                
        except Exception as e:
            logger.error(f"Error determining signal: {e}")
            return TimeframeSignal.NEUTRAL
    
    def _calculate_confidence(self, df: pd.DataFrame, trend_strength: float, timeframe: str) -> float:
        """Calculate confidence in the analysis"""
        try:
            confidence = 0.5  # Base confidence
            
            # Add trend strength component
            confidence += trend_strength * 0.3
            
            # Add volume confirmation
            volume_trend = self._analyze_volume_trend(df)
            if volume_trend in ['high', 'above_average']:
                confidence += 0.1
            
            # Add timeframe weight
            tf_weight = self.timeframe_weights.get(timeframe, 0.25)
            confidence += tf_weight * 0.1
            
            # Ensure confidence is between 0 and 1
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5

# Example usage
if __name__ == "__main__":
    analyzer = MultiTimeframeAnalyzer()
    
    # Analyze BTC/USDT
    symbol = "BTC/USDT"
    analyses = analyzer.analyze_symbol(symbol)
    
    # Print results
    for timeframe, analysis in analyses.items():
        print(f"\n{timeframe} Analysis:")
        print(f"  Trend: {analysis.trend_direction} (strength: {analysis.trend_strength:.2f})")
        print(f"  Signal: {analysis.signal.value}")
        print(f"  RSI: {analysis.rsi:.1f}")
        print(f"  MACD: {analysis.macd_signal}")
        print(f"  Confidence: {analysis.confidence:.2f}")
    
    # Get overall signal
    overall = analyzer.get_overall_signal(analyses)
    print(f"\nOverall Signal: {overall['signal'].value}")
    print(f"Confidence: {overall['confidence']:.2f}")

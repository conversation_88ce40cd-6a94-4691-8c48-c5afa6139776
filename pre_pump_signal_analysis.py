import os
import pandas as pd
import numpy as np
from pathlib import Path
from ta.momentum import RSIIndicator
from ta.trend import MACD, ADXIndicator
from ta.volume import OnBalanceVolumeIndicator, ChaikinMoneyFlowIndicator
from collections import Counter, defaultdict
import json

DATA_DIR = Path('data')
REPORT_FILE = 'pre_pump_report.json'
PUMP_THRESHOLD = 0.10  # 10%
WINDOWS = [48, 24, 8]  # hours before pump

# Helper to extract signals

def extract_signals(df: pd.DataFrame, idx: int) -> dict:
    signals = {}
    signals['rsi'] = df['rsi'].iloc[idx]
    signals['macd'] = df['macd'].iloc[idx]
    signals['macd_signal'] = df['macd_signal'].iloc[idx]
    signals['adx'] = df['adx'].iloc[idx]
    signals['adx_pos'] = df['adx_pos'].iloc[idx]
    signals['adx_neg'] = df['adx_neg'].iloc[idx]
    signals['obv'] = df['obv'].iloc[idx]
    signals['cmf'] = df['cmf'].iloc[idx]
    return signals

# Main analysis
results = {str(w): [] for w in WINDOWS}
combo_counter = {str(w): Counter() for w in WINDOWS}
signal_stats = {str(w): defaultdict(list) for w in WINDOWS}

for file in DATA_DIR.glob('*.csv'):
    df = pd.read_csv(file)
    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
    
    # حساب المؤشرات
    df['rsi'] = RSIIndicator(df['close'], window=14).rsi()
    macd = MACD(df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()
    adx = ADXIndicator(df['high'], df['low'], df['close'])
    df['adx'] = adx.adx()
    df['adx_pos'] = adx.adx_pos()
    df['adx_neg'] = adx.adx_neg()
    df['obv'] = OnBalanceVolumeIndicator(df['close'], df['volume']).on_balance_volume()
    df['cmf'] = ChaikinMoneyFlowIndicator(df['high'], df['low'], df['close'], df['volume']).chaikin_money_flow()
    
    # البحث عن الصعودات الكبيرة
    for i in range(len(df) - 48):
        for lookahead in [48, 24, 8]:
            if i + lookahead >= len(df):
                continue
            price_now = df['close'].iloc[i]
            price_future = df['close'].iloc[i + lookahead]
            change = (price_future - price_now) / price_now
            if change >= PUMP_THRESHOLD:
                # رصد المؤشرات قبل الصعود
                for w in WINDOWS:
                    idx = i - w if i - w >= 0 else 0
                    signals = extract_signals(df, idx)
                    results[str(w)].append(signals)
                    # تجميع الإشارات المجمعة
                    combo = tuple((k, round(v,2)) for k,v in signals.items())
                    combo_counter[str(w)][combo] += 1
                    for k, v in signals.items():
                        signal_stats[str(w)][k].append(v)
                break  # لا نكرر نفس الصعود لنفس الشمعة

# تحليل النتائج
report = {}
for w in WINDOWS:
    wstr = str(w)
    combos = combo_counter[wstr].most_common(10)
    signals_summary = {k: {'mean': float(np.mean(v)), 'std': float(np.std(v))} for k,v in signal_stats[wstr].items()}
    report[wstr] = {
        'top_combos': [ {'combo': dict(c), 'count': cnt} for c, cnt in combos ],
        'signals_stats': signals_summary,
        'total_pumps': len(results[wstr])
    }

with open(REPORT_FILE, 'w', encoding='utf-8') as f:
    json.dump(report, f, indent=2, ensure_ascii=False)

print(f'تم الانتهاء من التحليل. النتائج في {REPORT_FILE}')

import ccxt
import pandas as pd
import ta
import requests
import time
import os
import json
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Telegram Configuration
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

if not BOT_TOKEN or not CHAT_ID:
    logger.error("Missing Telegram credentials in environment variables")
    raise ValueError("Please set TELEGRAM_TOKEN and TELEGRAM_CHAT_ID in .env file")

# System Configuration
TIMEFRAME = '4h'
LIMIT = 100  # Number of candles to analyze
MIN_PROBABILITY = 0.60  # Minimum success probability threshold
MIN_EXPECTED_RETURN = 0.03  # Minimum expected return (3%)
COOLDOWN_HOURS = 12  # Hours between alerts for the same symbol
MAX_RETRIES = 3  # Maximum API call retries
RETRY_DELAY = 5  # Seconds between retries

class CryptoMonitor:
    def __init__(self):
        """Initialize the crypto monitoring system"""
        self.exchange = self._setup_exchange()
        self.last_alerts = {}
        self.signal_combos = self._load_signal_combos()
        self.symbols = self._load_symbols()
    
    def _setup_exchange(self) -> ccxt.Exchange:
        """Configure and return exchange instance"""
        try:
            exchange = ccxt.binance({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                    'adjustForTimeDifference': True
                }
            })
            return exchange
        except Exception as e:
            logger.error(f"Failed to initialize exchange: {e}")
            raise
    
    def _load_signal_combos(self) -> List[Dict]:
        """Load top performing signal combinations"""
        try:
            with open('top_combos.json', 'r', encoding='utf-8') as f:
                combos = json.load(f)
            logger.info(f"Loaded {len(combos)} signal combinations")
            return combos
        except Exception as e:
            logger.error(f"Failed to load signal combinations: {e}")
            raise
    
    def _load_symbols(self) -> List[str]:
        """Load trading symbols from file"""
        try:
            with open('symbols.txt', 'r') as f:
                symbols = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            logger.info(f"Loaded {len(symbols)} symbols for monitoring")
            return symbols
        except Exception as e:
            logger.error(f"Failed to load symbols: {e}")
            raise
    
        def fetch_ohlcv(self, symbol: str) -> Optional[pd.DataFrame]:
        """Fetch OHLCV data with retry mechanism"""
        for attempt in range(MAX_RETRIES):
            try:
                ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=TIMEFRAME, limit=LIMIT)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                return df
                
            except Exception as e:
                logger.error(f"Error fetching data for {symbol} (attempt {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY)
        
        return None

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        try:
            # RSI
            df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
            
            # MACD
            macd = ta.trend.MACD(close=df['close'])
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            
            # ADX
            adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
            df['adx'] = adx.adx()
            df['adx_pos'] = adx.adx_pos()
            df['adx_neg'] = adx.adx_neg()
            
            # OBV
            df['obv'] = ta.volume.OnBalanceVolumeIndicator(
                close=df['close'],
                volume=df['volume']
            ).on_balance_volume()
            df['obv_sma'] = df['obv'].rolling(window=20).mean()
            
            # CMF
            df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                volume=df['volume']
            ).chaikin_money_flow()
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return None

    def check_combination_signals(self, df: pd.DataFrame, combo: Dict) -> Dict:
        """Check if a symbol meets the criteria for a specific combination"""
        try:
            # Get the last row for current conditions
            current = df.iloc[-1]
            
            # Parse indicators from combo name
            indicators = combo['combo'].split('+')
            
            # Check each indicator's condition
            conditions = []
            for indicator in indicators:
                if indicator == 'rsi':
                    conditions.append(current['rsi'] < 30)
                elif indicator == 'macd':
                    conditions.append(current['macd'] > current['macd_signal'])
                elif indicator == 'obv':
                    conditions.append(current['obv'] > current['obv_sma'])
                elif indicator == 'adx':
                    conditions.append(current['adx'] > 25 and current['adx_pos'] > current['adx_neg'])
                elif indicator == 'cmf':
                    conditions.append(current['cmf'] > 0)
            
            # All conditions must be True for a signal
            if all(conditions):
                return {
                    'triggered': True,
                    'probability': combo['success_probability'],
                    'expected_return': combo['average_return'],
                    'score': combo['score'],
                    'combo': combo['combo']
                }
            
            return {'triggered': False}
            
        except Exception as e:
            logger.error(f"Error checking combination signals: {e}")
            return {'triggered': False}

    def format_alert_message(self, symbol: str, signal: Dict) -> str:
        """Format alert message for Telegram"""
        return f"""🚨 *Strong Signal Detected*

Symbol: `{symbol}`
Strategy: {signal['combo']}
Probability: {signal['probability']:.1%}
Expected Return: {signal['expected_return']:.1%}
Quality Score: {signal['score']:.2f}

Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    def can_send_alert(self, symbol: str) -> bool:
        """Check if enough time has passed since the last alert"""
        if symbol not in self.last_alerts:
            return True
            
        time_since_last = datetime.now() - self.last_alerts[symbol]
        return time_since_last > timedelta(hours=COOLDOWN_HOURS)

    def monitor_symbol(self, symbol: str) -> None:
        """Monitor a single symbol for signals"""
        try:
            # Skip if in cooldown
            if not self.can_send_alert(symbol):
                return
                
            # Fetch and process data
            df = self.fetch_ohlcv(symbol)
            if df is None:
                return
                
            df = self.calculate_indicators(df)
            if df is None:
                return
            
            # Check each combination
            for combo in self.signal_combos:
                signal = self.check_combination_signals(df, combo)
                
                if signal['triggered']:
                    # Validate signal quality
                    if (signal['probability'] >= MIN_PROBABILITY and
                        signal['expected_return'] >= MIN_EXPECTED_RETURN):
                        
                        # Send alert
                        message = self.format_alert_message(symbol, signal)
                        if self.send_telegram_alert(message):
                            self.last_alerts[symbol] = datetime.now()
                            logger.info(f"Alert sent for {symbol}")
                        
                        # Only send one alert per symbol per cycle
                        break
                        
        except Exception as e:
            logger.error(f"Error monitoring {symbol}: {e}")

    def run(self) -> None:
        """Main monitoring loop"""
        logger.info(f"Starting monitoring with {len(self.symbols)} symbols")
        logger.info(f"Using {len(self.signal_combos)} signal combinations")
        
        while True:
            try:
                start_time = time.time()
                
                # Monitor each symbol
                for symbol in self.symbols:
                    self.monitor_symbol(symbol)
                    time.sleep(0.5)  # Rate limiting
                
                # Calculate and adjust sleep time
                execution_time = time.time() - start_time
                sleep_time = max(0, 300 - execution_time)  # 5 minutes minus execution time
                
                logger.info(f"Completed monitoring cycle in {execution_time:.1f}s")
                time.sleep(sleep_time)
                
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring cycle: {e}")
                time.sleep(60)  # Wait before retrying

def main():
    """Entry point"""
    try:
        monitor = CryptoMonitor()
        monitor.run()
    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    main()
            logger.info("✅ تم إرسال التنبيه")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                # الاحتفاظ بالتنسيق الأصلي للعملة (مع الشرطة المائلة)
                symbols.append(line)
        return symbols

def calculate_indicators(df):
    """حساب المؤشرات الفنية"""
    try:
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    except Exception as e:
        logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def detect_signals(df):
    """كشف الإشارات الواقعية للمؤشرات الخمسة"""
    if df is None or len(df) < 10:
        return None, []

    signals = {}
    active_signals = []

    try:
        # RSI عالي الجودة
        if 'rsi' in df.columns and len(df) > 2:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2]
            prev2_rsi = df['rsi'].iloc[-3]
            signals['rsi_realistic'] = (
                35 <= current_rsi <= 65 and     # منطقة صحية
                current_rsi > prev_rsi > prev2_rsi  # تحسن مستمر
            )
            if signals['rsi_realistic']:
                active_signals.append(f"RSI قوي: {current_rsi:.1f}")
        else:
            signals['rsi_realistic'] = False

        # MACD عالي الجودة
        if 'macd' in df.columns and 'macd_signal' in df.columns and len(df) > 2:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2]
            prev_signal = df['macd_signal'].iloc[-2]

            # تقاطع إيجابي قوي أو تحسن مستمر فوق الصفر
            bullish_cross = (current_macd > current_signal and prev_macd <= prev_signal)
            strong_uptrend = (current_macd > 0 and current_macd > prev_macd and current_macd > current_signal)

            signals['macd_realistic'] = bullish_cross or strong_uptrend
            if signals['macd_realistic']:
                active_signals.append("MACD قوي")
        else:
            signals['macd_realistic'] = False

        # OBV عالي الجودة
        if 'obv' in df.columns and len(df) >= 5:
            obv_values = df['obv'].iloc[-5:].values
            # اتجاه صاعد مستمر في آخر 5 فترات
            signals['obv_realistic'] = all(obv_values[i] < obv_values[i+1] for i in range(3))
            if signals['obv_realistic']:
                active_signals.append("OBV قوي ومستمر")
        else:
            signals['obv_realistic'] = False

        # ADX عالي الجودة (إذا متوفر)
        if all(col in df.columns for col in ['adx', 'adx_pos', 'adx_neg']) and len(df) > 1:
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            prev_adx = df['adx'].iloc[-2]

            signals['adx_realistic'] = (
                current_adx > 25 and           # قوة اتجاه عالية
                current_pos > current_neg * 1.5 and  # هيمنة صاعدة
                current_adx > prev_adx         # قوة متزايدة
            )
            if signals['adx_realistic']:
                active_signals.append(f"ADX قوي جداً: {current_adx:.1f}")
        else:
            signals['adx_realistic'] = False

        # CMF عالي الجودة (إذا متوفر)
        if 'cmf' in df.columns and len(df) >= 3:
            cmf_values = df['cmf'].iloc[-3:].values
            # تدفق نقدي إيجابي قوي ومستمر
            signals['cmf_realistic'] = all(val > 0.1 for val in cmf_values)
            if signals['cmf_realistic']:
                active_signals.append(f"CMF قوي: {cmf_values[-1]:.3f}")
        else:
            signals['cmf_realistic'] = False

        return signals, active_signals

    except Exception as e:
        logger.error(f"❌ خطأ في كشف الإشارات الواقعية: {e}")
        return None, []

def calculate_prediction_metrics(signals):
    """حساب مقاييس التنبؤ"""
    if not signals or not advanced_weights:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    active_indicators = [indicator for indicator, is_active in signals.items() if is_active]
    
    if not active_indicators:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    total_weight = 0
    weighted_probability = 0
    weighted_return = 0
    weighted_time = 0
    
    for indicator in active_indicators:
        if indicator in advanced_weights:
            stats = advanced_weights[indicator]
            weight = stats.get('weight', 0)
            
            total_weight += weight
            weighted_probability += stats.get('success_probability', 0) * weight
            weighted_return += stats.get('average_return', 0) * weight
            weighted_time += stats.get('average_time_to_target', 0) * weight
    
    if total_weight == 0:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    # حساب المتوسطات المرجحة (البيانات جاهزة كنسب مئوية)
    probability = weighted_probability / total_weight  # البيانات جاهزة كنسب مئوية
    expected_return = weighted_return / total_weight    # البيانات جاهزة كنسب مئوية
    expected_time = weighted_time / total_weight
    
    confidence_score = min(100, (probability * 0.4 + (expected_return / 10) * 0.3 + 
                               (len(active_indicators) / 9) * 0.3) * 100)
    
    if confidence_score >= 80:
        risk_level = 'منخفض'
    elif confidence_score >= 60:
        risk_level = 'متوسط'
    else:
        risk_level = 'عالي'
    
    return {
        'probability': probability,
        'expected_return': expected_return,
        'expected_time': expected_time * 4,  # تحويل إلى ساعات
        'confidence_score': confidence_score,
        'risk_level': risk_level,
        'active_indicators_count': len(active_indicators)
    }

def analyze_symbol(symbol):
    """تحليل عملة واحدة"""
    exchange = ccxt.binance()

    try:
        ohlcv = exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=LIMIT)
        if not ohlcv or len(ohlcv) < 50:
            logger.warning(f"⚠️ بيانات غير كافية لـ {symbol}")
            return
        
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        df = calculate_indicators(df)
        if df is None:
            return
        
        signals, active_signals = detect_signals(df)
        if signals is None:
            return
        
        metrics = calculate_prediction_metrics(signals)
        
        if (metrics['probability'] >= MIN_PROBABILITY_THRESHOLD and 
            metrics['expected_return'] >= MIN_EXPECTED_RETURN):
            
            current_price = df['close'].iloc[-1]
            predicted_price = current_price * (1 + metrics['expected_return'] / 100)
            
            message = f"🚨 *إشارة صاعدة جديدة*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"🔮 *السعر المتوقع (48س):* ${predicted_price:.6f}\n\n"
            message += f"🎯 *احتمالية الصعود:* {metrics['probability']:.1%}\n"
            message += f"📊 *العائد المتوقع:* {metrics['expected_return']:+.2f}%\n"
            message += f"⏰ *المدة المتوقعة:* {metrics['expected_time']:.1f} ساعة\n"
            message += f"🔒 *نقاط الثقة:* {metrics['confidence_score']:.0f}/100\n"
            message += f"⚠️ *مستوى المخاطر:* {metrics['risk_level']}\n\n"
            
            if active_signals:
                message += f"✅ *الإشارات النشطة* ({metrics['active_indicators_count']}/9):\n"
                for signal in active_signals:
                    message += f"• {signal}\n"
            
            message += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            logger.info(f"📤 إرسال تنبيه لـ {symbol}")
            send_telegram(message)
        else:
            logger.info(f"📊 {symbol}: احتمالية {metrics['probability']:.1%}, عائد {metrics['expected_return']:+.2f}% - تحت العتبة")

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def main():
    """الوظيفة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات")

    if not load_advanced_weights():
        logger.error("❌ فشل في تحميل الأوزان")
        return

    logger.info(f"🔧 العتبات: احتمالية {MIN_PROBABILITY_THRESHOLD:.1%}, عائد {MIN_EXPECTED_RETURN}%")
    
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return
    
    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة")
    
    try:
        while True:
            logger.info(f"🔄 بدء دورة جديدة - فحص {len(symbols)} عملة")

            for i, symbol in enumerate(symbols, 1):
                logger.info(f"🔍 [{i}/{len(symbols)}] فحص {symbol}")
                analyze_symbol(symbol)
                time.sleep(2)
            
            logger.info("✅ انتهت الدورة - انتظار 15 دقيقة")
            time.sleep(900)
            
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام")
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()

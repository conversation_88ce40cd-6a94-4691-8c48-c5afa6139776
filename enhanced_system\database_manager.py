"""
Advanced Database and Caching System for Crypto Trading
Provides efficient data storage, retrieval, and caching capabilities
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
import json
import pickle
import hashlib
from dataclasses import dataclass, asdict
import asyncio
import aioredis
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import JSON
import redis

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database models
Base = declarative_base()

class MarketData(Base):
    """Market data table"""
    __tablename__ = 'market_data'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)
    indicators = Column(JSON)

class TradingSignals(Base):
    """Trading signals table"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    signal_type = Column(String(50), nullable=False)
    direction = Column(String(10), nullable=False)  # 'buy', 'sell', 'hold'
    confidence = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    indicators_used = Column(JSON)
    ml_prediction = Column(JSON)
    risk_metrics = Column(JSON)

class Trades(Base):
    """Trades table"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    entry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime)
    side = Column(String(10), nullable=False)  # 'long', 'short'
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float)
    quantity = Column(Float, nullable=False)
    pnl = Column(Float)
    pnl_percent = Column(Float)
    commission = Column(Float)
    status = Column(String(20), default='open')  # 'open', 'closed', 'cancelled'
    strategy = Column(String(50))
    metadata = Column(JSON)

class AlertHistory(Base):
    """Alert history table"""
    __tablename__ = 'alert_history'
    
    id = Column(Integer, primary_key=True)
    alert_id = Column(String(100), unique=True, nullable=False)
    symbol = Column(String(20), nullable=False, index=True)
    alert_type = Column(String(50), nullable=False)
    priority = Column(String(20), nullable=False)
    title = Column(String(200), nullable=False)
    message = Column(Text)
    timestamp = Column(DateTime, nullable=False, index=True)
    confidence_score = Column(Float)
    sent_channels = Column(JSON)
    outcome = Column(String(20))  # 'success', 'failure', 'timeout'
    profit_loss = Column(Float)

@dataclass
class CacheConfig:
    """Cache configuration"""
    redis_host: str = 'localhost'
    redis_port: int = 6379
    redis_db: int = 0
    default_ttl: int = 3600  # 1 hour
    max_memory: str = '256mb'

class DatabaseManager:
    """Advanced database manager with caching"""
    
    def __init__(self, db_url: str, cache_config: CacheConfig = None):
        """
        Initialize database manager
        
        Args:
            db_url: Database connection URL
            cache_config: Redis cache configuration
        """
        self.db_url = db_url
        self.cache_config = cache_config or CacheConfig()
        
        # Database setup
        self.engine = create_engine(db_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        
        # Cache setup
        self.redis_client = None
        self._setup_cache()
        
        logger.info("Database manager initialized successfully")
    
    def _setup_cache(self):
        """Setup Redis cache"""
        try:
            self.redis_client = redis.Redis(
                host=self.cache_config.redis_host,
                port=self.cache_config.redis_port,
                db=self.cache_config.redis_db,
                decode_responses=True
            )
            
            # Test connection
            self.redis_client.ping()
            logger.info("Redis cache connected successfully")
            
        except Exception as e:
            logger.warning(f"Redis cache not available: {e}")
            self.redis_client = None
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    # Market Data Operations
    def store_market_data(self, symbol: str, timeframe: str, data: pd.DataFrame) -> bool:
        """
        Store market data in database
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            data: OHLCV data with indicators
            
        Returns:
            Success status
        """
        try:
            session = self.get_session()
            
            for index, row in data.iterrows():
                # Check if record exists
                existing = session.query(MarketData).filter(
                    MarketData.symbol == symbol,
                    MarketData.timestamp == index,
                    MarketData.timeframe == timeframe
                ).first()
                
                if existing:
                    # Update existing record
                    existing.open = row['open']
                    existing.high = row['high']
                    existing.low = row['low']
                    existing.close = row['close']
                    existing.volume = row['volume']
                    
                    # Update indicators
                    indicators = {}
                    for col in data.columns:
                        if col not in ['open', 'high', 'low', 'close', 'volume']:
                            if not pd.isna(row[col]):
                                indicators[col] = float(row[col])
                    existing.indicators = indicators
                    
                else:
                    # Create new record
                    indicators = {}
                    for col in data.columns:
                        if col not in ['open', 'high', 'low', 'close', 'volume']:
                            if not pd.isna(row[col]):
                                indicators[col] = float(row[col])
                    
                    market_data = MarketData(
                        symbol=symbol,
                        timestamp=index,
                        timeframe=timeframe,
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=row['volume'],
                        indicators=indicators
                    )
                    session.add(market_data)
            
            session.commit()
            session.close()
            
            # Invalidate cache
            self._invalidate_cache(f"market_data:{symbol}:{timeframe}")
            
            logger.info(f"Stored {len(data)} records for {symbol} {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
            session.rollback()
            session.close()
            return False
    
    def get_market_data(self, symbol: str, timeframe: str, 
                       start_date: datetime = None, end_date: datetime = None,
                       limit: int = None, use_cache: bool = True) -> Optional[pd.DataFrame]:
        """
        Retrieve market data from database with caching
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of records
            use_cache: Whether to use cache
            
        Returns:
            DataFrame with market data
        """
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(
                'market_data', symbol, timeframe, start_date, end_date, limit
            )
            
            # Try cache first
            if use_cache and self.redis_client:
                cached_data = self._get_from_cache(cache_key)
                if cached_data is not None:
                    logger.debug(f"Retrieved market data from cache: {cache_key}")
                    return cached_data
            
            # Query database
            session = self.get_session()
            query = session.query(MarketData).filter(
                MarketData.symbol == symbol,
                MarketData.timeframe == timeframe
            )
            
            if start_date:
                query = query.filter(MarketData.timestamp >= start_date)
            if end_date:
                query = query.filter(MarketData.timestamp <= end_date)
            
            query = query.order_by(MarketData.timestamp)
            
            if limit:
                query = query.limit(limit)
            
            results = query.all()
            session.close()
            
            if not results:
                return None
            
            # Convert to DataFrame
            data_list = []
            for record in results:
                row_data = {
                    'open': record.open,
                    'high': record.high,
                    'low': record.low,
                    'close': record.close,
                    'volume': record.volume
                }
                
                # Add indicators
                if record.indicators:
                    row_data.update(record.indicators)
                
                data_list.append(row_data)
            
            df = pd.DataFrame(data_list, index=[r.timestamp for r in results])
            
            # Cache the result
            if use_cache and self.redis_client:
                self._store_in_cache(cache_key, df)
            
            logger.info(f"Retrieved {len(df)} market data records for {symbol} {timeframe}")
            return df
            
        except Exception as e:
            logger.error(f"Error retrieving market data: {e}")
            return None
    
    # Trading Signals Operations
    def store_trading_signal(self, signal_data: Dict) -> bool:
        """Store trading signal in database"""
        try:
            session = self.get_session()
            
            signal = TradingSignals(
                symbol=signal_data['symbol'],
                timestamp=signal_data['timestamp'],
                signal_type=signal_data['signal_type'],
                direction=signal_data['direction'],
                confidence=signal_data['confidence'],
                price=signal_data['price'],
                indicators_used=signal_data.get('indicators_used', {}),
                ml_prediction=signal_data.get('ml_prediction', {}),
                risk_metrics=signal_data.get('risk_metrics', {})
            )
            
            session.add(signal)
            session.commit()
            session.close()
            
            logger.info(f"Stored trading signal for {signal_data['symbol']}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing trading signal: {e}")
            session.rollback()
            session.close()
            return False
    
    def get_trading_signals(self, symbol: str = None, 
                          start_date: datetime = None, 
                          end_date: datetime = None,
                          signal_type: str = None) -> List[Dict]:
        """Retrieve trading signals from database"""
        try:
            session = self.get_session()
            query = session.query(TradingSignals)
            
            if symbol:
                query = query.filter(TradingSignals.symbol == symbol)
            if start_date:
                query = query.filter(TradingSignals.timestamp >= start_date)
            if end_date:
                query = query.filter(TradingSignals.timestamp <= end_date)
            if signal_type:
                query = query.filter(TradingSignals.signal_type == signal_type)
            
            results = query.order_by(TradingSignals.timestamp.desc()).all()
            session.close()
            
            signals = []
            for signal in results:
                signals.append({
                    'id': signal.id,
                    'symbol': signal.symbol,
                    'timestamp': signal.timestamp,
                    'signal_type': signal.signal_type,
                    'direction': signal.direction,
                    'confidence': signal.confidence,
                    'price': signal.price,
                    'indicators_used': signal.indicators_used,
                    'ml_prediction': signal.ml_prediction,
                    'risk_metrics': signal.risk_metrics
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error retrieving trading signals: {e}")
            return []
    
    # Trade Operations
    def store_trade(self, trade_data: Dict) -> bool:
        """Store trade in database"""
        try:
            session = self.get_session()
            
            trade = Trades(
                symbol=trade_data['symbol'],
                entry_time=trade_data['entry_time'],
                exit_time=trade_data.get('exit_time'),
                side=trade_data['side'],
                entry_price=trade_data['entry_price'],
                exit_price=trade_data.get('exit_price'),
                quantity=trade_data['quantity'],
                pnl=trade_data.get('pnl'),
                pnl_percent=trade_data.get('pnl_percent'),
                commission=trade_data.get('commission'),
                status=trade_data.get('status', 'open'),
                strategy=trade_data.get('strategy'),
                metadata=trade_data.get('metadata', {})
            )
            
            session.add(trade)
            session.commit()
            session.close()
            
            logger.info(f"Stored trade for {trade_data['symbol']}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing trade: {e}")
            session.rollback()
            session.close()
            return False
    
    def update_trade(self, trade_id: int, update_data: Dict) -> bool:
        """Update existing trade"""
        try:
            session = self.get_session()
            
            trade = session.query(Trades).filter(Trades.id == trade_id).first()
            if not trade:
                logger.warning(f"Trade {trade_id} not found")
                return False
            
            for key, value in update_data.items():
                if hasattr(trade, key):
                    setattr(trade, key, value)
            
            session.commit()
            session.close()
            
            logger.info(f"Updated trade {trade_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating trade: {e}")
            session.rollback()
            session.close()
            return False
    
    def get_trades(self, symbol: str = None, status: str = None,
                  start_date: datetime = None, end_date: datetime = None) -> List[Dict]:
        """Retrieve trades from database"""
        try:
            session = self.get_session()
            query = session.query(Trades)
            
            if symbol:
                query = query.filter(Trades.symbol == symbol)
            if status:
                query = query.filter(Trades.status == status)
            if start_date:
                query = query.filter(Trades.entry_time >= start_date)
            if end_date:
                query = query.filter(Trades.entry_time <= end_date)
            
            results = query.order_by(Trades.entry_time.desc()).all()
            session.close()
            
            trades = []
            for trade in results:
                trades.append({
                    'id': trade.id,
                    'symbol': trade.symbol,
                    'entry_time': trade.entry_time,
                    'exit_time': trade.exit_time,
                    'side': trade.side,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'quantity': trade.quantity,
                    'pnl': trade.pnl,
                    'pnl_percent': trade.pnl_percent,
                    'commission': trade.commission,
                    'status': trade.status,
                    'strategy': trade.strategy,
                    'metadata': trade.metadata
                })
            
            return trades
            
        except Exception as e:
            logger.error(f"Error retrieving trades: {e}")
            return []
    
    # Cache Operations
    def _generate_cache_key(self, *args) -> str:
        """Generate cache key from arguments"""
        key_string = ':'.join(str(arg) for arg in args if arg is not None)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_from_cache(self, key: str) -> Any:
        """Get data from cache"""
        try:
            if not self.redis_client:
                return None
            
            cached_data = self.redis_client.get(key)
            if cached_data:
                return pickle.loads(cached_data.encode('latin1'))
            
            return None
            
        except Exception as e:
            logger.warning(f"Cache retrieval error: {e}")
            return None
    
    def _store_in_cache(self, key: str, data: Any, ttl: int = None) -> bool:
        """Store data in cache"""
        try:
            if not self.redis_client:
                return False
            
            ttl = ttl or self.cache_config.default_ttl
            serialized_data = pickle.dumps(data).decode('latin1')
            
            self.redis_client.setex(key, ttl, serialized_data)
            return True
            
        except Exception as e:
            logger.warning(f"Cache storage error: {e}")
            return False
    
    def _invalidate_cache(self, pattern: str):
        """Invalidate cache entries matching pattern"""
        try:
            if not self.redis_client:
                return
            
            keys = self.redis_client.keys(f"*{pattern}*")
            if keys:
                self.redis_client.delete(*keys)
                logger.debug(f"Invalidated {len(keys)} cache entries")
                
        except Exception as e:
            logger.warning(f"Cache invalidation error: {e}")
    
    # Analytics and Reporting
    def get_performance_metrics(self, symbol: str = None, 
                              start_date: datetime = None,
                              end_date: datetime = None) -> Dict:
        """Get performance metrics from database"""
        try:
            session = self.get_session()
            query = session.query(Trades).filter(Trades.status == 'closed')
            
            if symbol:
                query = query.filter(Trades.symbol == symbol)
            if start_date:
                query = query.filter(Trades.entry_time >= start_date)
            if end_date:
                query = query.filter(Trades.entry_time <= end_date)
            
            trades = query.all()
            session.close()
            
            if not trades:
                return {}
            
            # Calculate metrics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.pnl and t.pnl > 0])
            losing_trades = len([t for t in trades if t.pnl and t.pnl < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            total_pnl = sum(t.pnl for t in trades if t.pnl)
            avg_pnl = total_pnl / total_trades if total_trades > 0 else 0
            
            winning_pnls = [t.pnl for t in trades if t.pnl and t.pnl > 0]
            losing_pnls = [t.pnl for t in trades if t.pnl and t.pnl < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 0
            
            profit_factor = abs(sum(winning_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')
            
            metrics = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'avg_pnl': avg_pnl,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """Clean up old data from database"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            session = self.get_session()
            
            # Clean old market data
            deleted_market = session.query(MarketData).filter(
                MarketData.timestamp < cutoff_date
            ).delete()
            
            # Clean old signals
            deleted_signals = session.query(TradingSignals).filter(
                TradingSignals.timestamp < cutoff_date
            ).delete()
            
            # Clean old alerts
            deleted_alerts = session.query(AlertHistory).filter(
                AlertHistory.timestamp < cutoff_date
            ).delete()
            
            session.commit()
            session.close()
            
            logger.info(f"Cleaned up old data: {deleted_market} market records, "
                       f"{deleted_signals} signals, {deleted_alerts} alerts")
            
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            session.rollback()
            session.close()

# Example usage
if __name__ == "__main__":
    # Initialize database manager
    db_url = "postgresql://user:password@localhost/crypto_trading"
    # For SQLite: db_url = "sqlite:///crypto_trading.db"
    
    cache_config = CacheConfig(
        redis_host='localhost',
        redis_port=6379,
        default_ttl=3600
    )
    
    db_manager = DatabaseManager(db_url, cache_config)
    
    # Example: Store sample market data
    dates = pd.date_range('2023-01-01', periods=100, freq='4H')
    sample_data = pd.DataFrame({
        'open': np.random.randn(100) * 100 + 50000,
        'high': np.random.randn(100) * 100 + 50100,
        'low': np.random.randn(100) * 100 + 49900,
        'close': np.random.randn(100) * 100 + 50000,
        'volume': np.random.randint(1000, 10000, 100),
        'rsi': np.random.uniform(20, 80, 100)
    }, index=dates)
    
    # Store data
    success = db_manager.store_market_data('BTC/USDT', '4h', sample_data)
    print(f"Data storage success: {success}")
    
    # Retrieve data
    retrieved_data = db_manager.get_market_data('BTC/USDT', '4h', limit=50)
    if retrieved_data is not None:
        print(f"Retrieved {len(retrieved_data)} records")
        print(retrieved_data.head())
    
    # Get performance metrics
    metrics = db_manager.get_performance_metrics()
    print(f"Performance metrics: {metrics}")

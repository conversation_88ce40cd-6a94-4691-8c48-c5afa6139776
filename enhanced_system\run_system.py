#!/usr/bin/env python3
"""
Quick Start Script for Enhanced Crypto Trading System
Provides easy commands to run different components of the system
"""

import argparse
import asyncio
import sys
import os
import subprocess
from pathlib import Path

def run_dashboard():
    """Run the Streamlit dashboard"""
    print("🚀 Starting Enhanced Trading Dashboard...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "dashboard.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running dashboard: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped by user")
        return True

def run_trading_system():
    """Run the main trading system"""
    print("🚀 Starting Enhanced Trading System...")
    try:
        subprocess.run([sys.executable, "main_system.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running trading system: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Trading system stopped by user")
        return True

def run_backtest(symbol="BTC/USDT"):
    """Run a backtest"""
    print(f"📈 Running backtest for {symbol}...")
    try:
        from main_system import EnhancedTradingSystem
        
        async def backtest():
            system = EnhancedTradingSystem()
            results = await system.run_backtest(symbol)
            if results:
                print("✅ Backtest completed successfully")
            else:
                print("❌ Backtest failed")
        
        asyncio.run(backtest())
        return True
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return False

def setup_environment():
    """Setup the environment and dependencies"""
    print("🔧 Setting up Enhanced Trading System environment...")
    
    # Check if config file exists
    config_file = Path("config.json")
    if not config_file.exists():
        print("📝 Creating default configuration file...")
        example_config = Path("config.json.example")
        if example_config.exists():
            import shutil
            shutil.copy(example_config, config_file)
            print("✅ Configuration file created from example")
        else:
            print("⚠️ Example configuration file not found")
    
    # Check dependencies
    print("📦 Checking dependencies...")
    try:
        import pandas
        import numpy
        import streamlit
        import ccxt
        print("✅ Core dependencies found")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    
    # Check optional dependencies
    try:
        import tensorflow
        print("✅ TensorFlow found")
    except ImportError:
        print("⚠️ TensorFlow not found - ML features will be limited")
    
    try:
        import redis
        print("✅ Redis client found")
    except ImportError:
        print("⚠️ Redis not found - caching will be disabled")
    
    print("✅ Environment setup complete")
    return True

def show_status():
    """Show system status"""
    print("📊 Enhanced Trading System Status")
    print("=" * 40)
    
    # Check configuration
    config_file = Path("config.json")
    if config_file.exists():
        print("✅ Configuration file: Found")
    else:
        print("❌ Configuration file: Missing")
    
    # Check database
    db_file = Path("crypto_trading.db")
    if db_file.exists():
        print(f"✅ Database: Found ({db_file.stat().st_size / 1024:.1f} KB)")
    else:
        print("⚠️ Database: Not found (will be created)")
    
    # Check dependencies
    dependencies = [
        ("pandas", "Core data processing"),
        ("numpy", "Numerical computing"),
        ("streamlit", "Dashboard interface"),
        ("ccxt", "Exchange connectivity"),
        ("tensorflow", "Machine learning"),
        ("redis", "Caching system"),
        ("sqlalchemy", "Database ORM")
    ]
    
    print("\n📦 Dependencies:")
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            print(f"❌ {package}: {description} (Missing)")
    
    print("\n🔧 Quick Commands:")
    print("  python run_system.py dashboard    - Start dashboard")
    print("  python run_system.py trading     - Start trading system")
    print("  python run_system.py backtest    - Run backtest")
    print("  python run_system.py setup       - Setup environment")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Enhanced Crypto Trading System - Quick Start",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_system.py dashboard              # Start the dashboard
  python run_system.py trading               # Start trading system
  python run_system.py backtest              # Run backtest for BTC/USDT
  python run_system.py backtest --symbol ETH/USDT  # Run backtest for ETH/USDT
  python run_system.py setup                 # Setup environment
  python run_system.py status                # Show system status
        """
    )
    
    parser.add_argument(
        "command",
        choices=["dashboard", "trading", "backtest", "setup", "status"],
        help="Command to execute"
    )
    
    parser.add_argument(
        "--symbol",
        default="BTC/USDT",
        help="Symbol for backtesting (default: BTC/USDT)"
    )
    
    args = parser.parse_args()
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🚀 Enhanced Crypto Trading System")
    print("=" * 40)
    
    if args.command == "dashboard":
        return run_dashboard()
    
    elif args.command == "trading":
        return run_trading_system()
    
    elif args.command == "backtest":
        return run_backtest(args.symbol)
    
    elif args.command == "setup":
        return setup_environment()
    
    elif args.command == "status":
        show_status()
        return True
    
    else:
        parser.print_help()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

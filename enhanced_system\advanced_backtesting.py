"""
Advanced Backtesting System for Crypto Trading Strategies
Provides comprehensive performance analysis with detailed metrics
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
import logging
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Trade:
    """Individual trade record"""
    entry_time: datetime
    exit_time: datetime
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: float
    exit_price: float
    quantity: float
    pnl: float
    pnl_percent: float
    commission: float
    duration_hours: float
    entry_reason: str
    exit_reason: str

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    # Basic metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Returns
    total_return: float
    annualized_return: float
    max_drawdown: float
    max_drawdown_duration: int
    
    # Risk metrics
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    var_95: float  # Value at Risk
    cvar_95: float  # Conditional Value at Risk
    
    # Trade metrics
    avg_win: float
    avg_loss: float
    profit_factor: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float
    
    # Advanced metrics
    kelly_criterion: float
    expectancy: float
    recovery_factor: float
    ulcer_index: float
    
    # Time-based analysis
    monthly_returns: Dict[str, float]
    yearly_returns: Dict[str, float]
    
    # Trade list
    trades: List[Trade]

class AdvancedBacktester:
    """Advanced backtesting engine"""
    
    def __init__(self, initial_capital: float = 100000, commission: float = 0.001):
        """
        Initialize backtester
        
        Args:
            initial_capital: Starting capital
            commission: Commission rate (0.001 = 0.1%)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.trades = []
        self.equity_curve = []
        self.positions = {}
        
    def run_backtest(self, 
                    data: pd.DataFrame,
                    strategy_func: Callable,
                    start_date: str = None,
                    end_date: str = None) -> BacktestResults:
        """
        Run comprehensive backtest
        
        Args:
            data: Historical market data
            strategy_func: Strategy function that returns signals
            start_date: Start date for backtest
            end_date: End date for backtest
            
        Returns:
            BacktestResults object
        """
        try:
            logger.info("Starting advanced backtest...")
            
            # Filter data by date range
            if start_date:
                data = data[data.index >= start_date]
            if end_date:
                data = data[data.index <= end_date]
            
            # Initialize tracking variables
            self.trades = []
            self.equity_curve = []
            current_capital = self.initial_capital
            position = None
            
            # Run strategy on each bar
            for i in range(len(data)):
                current_bar = data.iloc[i]
                current_time = data.index[i]
                
                # Get strategy signal
                signal = strategy_func(data.iloc[:i+1])
                
                # Process signal
                if signal and not position:
                    # Enter position
                    position = self._enter_position(current_bar, current_time, signal, current_capital)
                    
                elif position and (signal is None or signal['action'] == 'exit'):
                    # Exit position
                    trade = self._exit_position(position, current_bar, current_time, 'signal')
                    if trade:
                        self.trades.append(trade)
                        current_capital += trade.pnl
                    position = None
                
                # Update equity curve
                if position:
                    unrealized_pnl = self._calculate_unrealized_pnl(position, current_bar)
                    equity = current_capital + unrealized_pnl
                else:
                    equity = current_capital
                
                self.equity_curve.append({
                    'timestamp': current_time,
                    'equity': equity,
                    'capital': current_capital
                })
            
            # Close any remaining position
            if position:
                final_bar = data.iloc[-1]
                final_time = data.index[-1]
                trade = self._exit_position(position, final_bar, final_time, 'end_of_data')
                if trade:
                    self.trades.append(trade)
            
            # Calculate comprehensive results
            results = self._calculate_results(data)
            
            logger.info(f"Backtest completed. Total trades: {len(self.trades)}")
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return None
    
    def _enter_position(self, bar: pd.Series, timestamp: datetime, signal: Dict, capital: float) -> Dict:
        """Enter a new position"""
        try:
            side = signal.get('side', 'long')
            entry_price = bar['close']
            
            # Calculate position size (simple fixed percentage for now)
            risk_percent = signal.get('risk_percent', 0.02)
            position_value = capital * risk_percent
            quantity = position_value / entry_price
            
            position = {
                'entry_time': timestamp,
                'side': side,
                'entry_price': entry_price,
                'quantity': quantity,
                'entry_reason': signal.get('reason', 'strategy_signal')
            }
            
            logger.debug(f"Entered {side} position: {quantity:.4f} @ {entry_price:.2f}")
            return position
            
        except Exception as e:
            logger.error(f"Error entering position: {e}")
            return None
    
    def _exit_position(self, position: Dict, bar: pd.Series, timestamp: datetime, reason: str) -> Trade:
        """Exit current position"""
        try:
            exit_price = bar['close']
            entry_price = position['entry_price']
            quantity = position['quantity']
            side = position['side']
            
            # Calculate P&L
            if side == 'long':
                pnl_percent = (exit_price - entry_price) / entry_price
            else:  # short
                pnl_percent = (entry_price - exit_price) / entry_price
            
            pnl = quantity * entry_price * pnl_percent
            commission_cost = quantity * entry_price * self.commission * 2  # Entry + Exit
            net_pnl = pnl - commission_cost
            
            # Calculate duration
            duration = (timestamp - position['entry_time']).total_seconds() / 3600  # hours
            
            trade = Trade(
                entry_time=position['entry_time'],
                exit_time=timestamp,
                symbol='CRYPTO',  # Placeholder
                side=side,
                entry_price=entry_price,
                exit_price=exit_price,
                quantity=quantity,
                pnl=net_pnl,
                pnl_percent=pnl_percent,
                commission=commission_cost,
                duration_hours=duration,
                entry_reason=position['entry_reason'],
                exit_reason=reason
            )
            
            logger.debug(f"Exited {side} position: P&L = {net_pnl:.2f} ({pnl_percent:.2%})")
            return trade
            
        except Exception as e:
            logger.error(f"Error exiting position: {e}")
            return None
    
    def _calculate_unrealized_pnl(self, position: Dict, bar: pd.Series) -> float:
        """Calculate unrealized P&L for open position"""
        try:
            current_price = bar['close']
            entry_price = position['entry_price']
            quantity = position['quantity']
            side = position['side']
            
            if side == 'long':
                pnl_percent = (current_price - entry_price) / entry_price
            else:
                pnl_percent = (entry_price - current_price) / entry_price
            
            return quantity * entry_price * pnl_percent
            
        except Exception as e:
            logger.error(f"Error calculating unrealized P&L: {e}")
            return 0.0
    
    def _calculate_results(self, data: pd.DataFrame) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        try:
            if not self.trades:
                logger.warning("No trades to analyze")
                return None
            
            # Convert equity curve to DataFrame
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df.set_index('timestamp', inplace=True)
            
            # Basic trade statistics
            total_trades = len(self.trades)
            winning_trades = len([t for t in self.trades if t.pnl > 0])
            losing_trades = len([t for t in self.trades if t.pnl < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Return calculations
            final_equity = equity_df['equity'].iloc[-1]
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # Calculate annualized return
            days = (equity_df.index[-1] - equity_df.index[0]).days
            years = days / 365.25
            annualized_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
            
            # Drawdown analysis
            equity_df['peak'] = equity_df['equity'].expanding().max()
            equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak']
            max_drawdown = equity_df['drawdown'].min()
            
            # Calculate drawdown duration
            drawdown_periods = []
            in_drawdown = False
            start_dd = None
            
            for i, dd in enumerate(equity_df['drawdown']):
                if dd < 0 and not in_drawdown:
                    in_drawdown = True
                    start_dd = i
                elif dd >= 0 and in_drawdown:
                    in_drawdown = False
                    if start_dd is not None:
                        drawdown_periods.append(i - start_dd)
            
            max_drawdown_duration = max(drawdown_periods) if drawdown_periods else 0
            
            # Risk metrics
            returns = equity_df['equity'].pct_change().dropna()
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            sortino_ratio = self._calculate_sortino_ratio(returns)
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Value at Risk
            var_95 = np.percentile(returns, 5)
            cvar_95 = returns[returns <= var_95].mean()
            
            # Trade metrics
            winning_pnls = [t.pnl for t in self.trades if t.pnl > 0]
            losing_pnls = [t.pnl for t in self.trades if t.pnl < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 0
            profit_factor = abs(sum(winning_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')
            
            largest_win = max(winning_pnls) if winning_pnls else 0
            largest_loss = min(losing_pnls) if losing_pnls else 0
            
            avg_trade_duration = np.mean([t.duration_hours for t in self.trades])
            
            # Advanced metrics
            kelly_criterion = self._calculate_kelly_criterion()
            expectancy = self._calculate_expectancy()
            recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            ulcer_index = self._calculate_ulcer_index(equity_df)
            
            # Time-based analysis
            monthly_returns = self._calculate_monthly_returns(equity_df)
            yearly_returns = self._calculate_yearly_returns(equity_df)
            
            results = BacktestResults(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_return=total_return,
                annualized_return=annualized_return,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_drawdown_duration,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                var_95=var_95,
                cvar_95=cvar_95,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                largest_win=largest_win,
                largest_loss=largest_loss,
                avg_trade_duration=avg_trade_duration,
                kelly_criterion=kelly_criterion,
                expectancy=expectancy,
                recovery_factor=recovery_factor,
                ulcer_index=ulcer_index,
                monthly_returns=monthly_returns,
                yearly_returns=yearly_returns,
                trades=self.trades
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating results: {e}")
            return None
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
            return np.sqrt(252) * excess_returns.mean() / returns.std() if returns.std() > 0 else 0
        except:
            return 0
    
    def _calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio"""
        try:
            excess_returns = returns - risk_free_rate / 252
            downside_returns = returns[returns < 0]
            downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
            return np.sqrt(252) * excess_returns.mean() / downside_std if downside_std > 0 else 0
        except:
            return 0
    
    def _calculate_kelly_criterion(self) -> float:
        """Calculate Kelly Criterion"""
        try:
            if not self.trades:
                return 0
            
            wins = [t for t in self.trades if t.pnl > 0]
            losses = [t for t in self.trades if t.pnl < 0]
            
            if not wins or not losses:
                return 0
            
            win_rate = len(wins) / len(self.trades)
            avg_win = np.mean([t.pnl_percent for t in wins])
            avg_loss = abs(np.mean([t.pnl_percent for t in losses]))
            
            kelly = win_rate - ((1 - win_rate) / (avg_win / avg_loss))
            return kelly
            
        except:
            return 0
    
    def _calculate_expectancy(self) -> float:
        """Calculate expectancy"""
        try:
            if not self.trades:
                return 0
            
            wins = [t.pnl for t in self.trades if t.pnl > 0]
            losses = [t.pnl for t in self.trades if t.pnl < 0]
            
            win_rate = len(wins) / len(self.trades)
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            
            expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
            return expectancy
            
        except:
            return 0
    
    def _calculate_ulcer_index(self, equity_df: pd.DataFrame) -> float:
        """Calculate Ulcer Index"""
        try:
            drawdowns = equity_df['drawdown'] * 100  # Convert to percentage
            squared_drawdowns = drawdowns ** 2
            ulcer_index = np.sqrt(squared_drawdowns.mean())
            return ulcer_index
        except:
            return 0
    
    def _calculate_monthly_returns(self, equity_df: pd.DataFrame) -> Dict[str, float]:
        """Calculate monthly returns"""
        try:
            monthly_equity = equity_df['equity'].resample('M').last()
            monthly_returns = monthly_equity.pct_change().dropna()
            return {date.strftime('%Y-%m'): ret for date, ret in monthly_returns.items()}
        except:
            return {}
    
    def _calculate_yearly_returns(self, equity_df: pd.DataFrame) -> Dict[str, float]:
        """Calculate yearly returns"""
        try:
            yearly_equity = equity_df['equity'].resample('Y').last()
            yearly_returns = yearly_equity.pct_change().dropna()
            return {date.strftime('%Y'): ret for date, ret in yearly_returns.items()}
        except:
            return {}
    
    def generate_report(self, results: BacktestResults, save_path: str = None) -> str:
        """Generate comprehensive backtest report"""
        try:
            report = "=" * 60 + "\n"
            report += "ADVANCED BACKTEST REPORT\n"
            report += "=" * 60 + "\n\n"
            
            # Basic Statistics
            report += "BASIC STATISTICS\n"
            report += "-" * 20 + "\n"
            report += f"Total Trades: {results.total_trades}\n"
            report += f"Winning Trades: {results.winning_trades}\n"
            report += f"Losing Trades: {results.losing_trades}\n"
            report += f"Win Rate: {results.win_rate:.2%}\n\n"
            
            # Returns
            report += "RETURNS\n"
            report += "-" * 20 + "\n"
            report += f"Total Return: {results.total_return:.2%}\n"
            report += f"Annualized Return: {results.annualized_return:.2%}\n"
            report += f"Max Drawdown: {results.max_drawdown:.2%}\n"
            report += f"Max DD Duration: {results.max_drawdown_duration} periods\n\n"
            
            # Risk Metrics
            report += "RISK METRICS\n"
            report += "-" * 20 + "\n"
            report += f"Sharpe Ratio: {results.sharpe_ratio:.2f}\n"
            report += f"Sortino Ratio: {results.sortino_ratio:.2f}\n"
            report += f"Calmar Ratio: {results.calmar_ratio:.2f}\n"
            report += f"VaR (95%): {results.var_95:.2%}\n"
            report += f"CVaR (95%): {results.cvar_95:.2%}\n\n"
            
            # Trade Analysis
            report += "TRADE ANALYSIS\n"
            report += "-" * 20 + "\n"
            report += f"Average Win: ${results.avg_win:.2f}\n"
            report += f"Average Loss: ${results.avg_loss:.2f}\n"
            report += f"Profit Factor: {results.profit_factor:.2f}\n"
            report += f"Largest Win: ${results.largest_win:.2f}\n"
            report += f"Largest Loss: ${results.largest_loss:.2f}\n"
            report += f"Avg Trade Duration: {results.avg_trade_duration:.1f} hours\n\n"
            
            # Advanced Metrics
            report += "ADVANCED METRICS\n"
            report += "-" * 20 + "\n"
            report += f"Kelly Criterion: {results.kelly_criterion:.2%}\n"
            report += f"Expectancy: ${results.expectancy:.2f}\n"
            report += f"Recovery Factor: {results.recovery_factor:.2f}\n"
            report += f"Ulcer Index: {results.ulcer_index:.2f}\n\n"
            
            if save_path:
                with open(save_path, 'w') as f:
                    f.write(report)
                logger.info(f"Report saved to {save_path}")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return "Error generating report"

# Example strategy function
def example_strategy(data: pd.DataFrame) -> Optional[Dict]:
    """Example strategy function"""
    if len(data) < 20:
        return None
    
    # Simple RSI strategy
    current_rsi = data['rsi'].iloc[-1] if 'rsi' in data.columns else 50
    
    if current_rsi < 30:
        return {
            'action': 'enter',
            'side': 'long',
            'reason': 'RSI oversold',
            'risk_percent': 0.02
        }
    elif current_rsi > 70:
        return {
            'action': 'exit',
            'reason': 'RSI overbought'
        }
    
    return None

# Example usage
if __name__ == "__main__":
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=1000, freq='4H')
    np.random.seed(42)
    
    # Generate sample OHLCV data with RSI
    close_prices = 50000 + np.cumsum(np.random.randn(1000) * 100)
    sample_data = pd.DataFrame({
        'open': close_prices + np.random.randn(1000) * 50,
        'high': close_prices + np.abs(np.random.randn(1000) * 100),
        'low': close_prices - np.abs(np.random.randn(1000) * 100),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 1000)
    }, index=dates)
    
    # Add RSI
    delta = sample_data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    sample_data['rsi'] = 100 - (100 / (1 + rs))
    
    # Run backtest
    backtester = AdvancedBacktester(initial_capital=100000)
    results = backtester.run_backtest(sample_data, example_strategy)
    
    if results:
        # Generate report
        report = backtester.generate_report(results)
        print(report)

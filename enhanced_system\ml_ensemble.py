"""
Machine Learning Ensemble System for Crypto Trading
Integrates LSTM, XGBoost, Random Forest, and other ML models
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
import joblib
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, accuracy_score, classification_report
import xgboost as xgb

# Deep Learning
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logging.warning("TensorFlow not available. LSTM models will be disabled.")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelPrediction:
    """Model prediction result"""
    model_name: str
    prediction: float
    confidence: float
    direction: str  # 'up', 'down', 'neutral'

@dataclass
class EnsemblePrediction:
    """Ensemble prediction result"""
    final_prediction: float
    confidence: float
    direction: str
    individual_predictions: List[ModelPrediction]
    model_weights: Dict[str, float]

class MLEnsemble:
    """Machine Learning Ensemble System"""
    
    def __init__(self):
        """Initialize the ensemble system"""
        self.models = {}
        self.scalers = {}
        self.model_weights = {
            'lstm': 0.35,
            'xgboost': 0.30,
            'random_forest': 0.25,
            'linear': 0.10
        }
        self.is_trained = False
        self.feature_columns = []
        
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare features for ML models
        
        Args:
            df: DataFrame with OHLCV and indicator data
            
        Returns:
            DataFrame with prepared features
        """
        try:
            df = df.copy()
            
            # Price-based features
            df['price_change'] = df['close'].pct_change()
            df['high_low_ratio'] = df['high'] / df['low']
            df['close_open_ratio'] = df['close'] / df['open']
            
            # Moving averages
            for period in [5, 10, 20, 50]:
                df[f'sma_{period}'] = df['close'].rolling(period).mean()
                df[f'price_sma_{period}_ratio'] = df['close'] / df[f'sma_{period}']
            
            # Volatility features
            df['volatility_5'] = df['close'].rolling(5).std()
            df['volatility_20'] = df['close'].rolling(20).std()
            
            # Volume features
            df['volume_sma_20'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma_20']
            
            # Technical indicators (if not already present)
            if 'rsi' not in df.columns:
                df = self._add_technical_indicators(df)
            
            # Lag features
            for lag in [1, 2, 3, 5]:
                df[f'close_lag_{lag}'] = df['close'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
                df[f'rsi_lag_{lag}'] = df['rsi'].shift(lag)
            
            # Target variable (next period return)
            df['target'] = df['close'].shift(-1) / df['close'] - 1
            df['target_direction'] = (df['target'] > 0).astype(int)
            
            # Select feature columns
            self.feature_columns = [col for col in df.columns if col not in 
                                  ['timestamp', 'target', 'target_direction', 'open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"Prepared {len(self.feature_columns)} features for ML models")
            return df
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return df
    
    def train_models(self, df: pd.DataFrame, test_size: float = 0.2) -> Dict[str, float]:
        """
        Train all ML models
        
        Args:
            df: DataFrame with prepared features
            test_size: Test set size ratio
            
        Returns:
            Dictionary with model performance scores
        """
        try:
            # Prepare data
            df_clean = df[self.feature_columns + ['target', 'target_direction']].dropna()
            
            if len(df_clean) < 100:
                logger.error("Insufficient data for training")
                return {}
            
            X = df_clean[self.feature_columns]
            y_reg = df_clean['target']
            y_class = df_clean['target_direction']
            
            # Split data
            X_train, X_test, y_reg_train, y_reg_test, y_class_train, y_class_test = train_test_split(
                X, y_reg, y_class, test_size=test_size, random_state=42, shuffle=False
            )
            
            # Scale features
            self.scalers['standard'] = StandardScaler()
            self.scalers['minmax'] = MinMaxScaler()
            
            X_train_scaled = self.scalers['standard'].fit_transform(X_train)
            X_test_scaled = self.scalers['standard'].transform(X_test)
            
            X_train_minmax = self.scalers['minmax'].fit_transform(X_train)
            X_test_minmax = self.scalers['minmax'].transform(X_test)
            
            performance = {}
            
            # Train Random Forest
            logger.info("Training Random Forest model...")
            rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            rf_model.fit(X_train, y_reg_train)
            rf_pred = rf_model.predict(X_test)
            rf_score = 1 - mean_squared_error(y_reg_test, rf_pred)
            self.models['random_forest'] = rf_model
            performance['random_forest'] = rf_score
            
            # Train XGBoost
            logger.info("Training XGBoost model...")
            xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            xgb_model.fit(X_train, y_reg_train)
            xgb_pred = xgb_model.predict(X_test)
            xgb_score = 1 - mean_squared_error(y_reg_test, xgb_pred)
            self.models['xgboost'] = xgb_model
            performance['xgboost'] = xgb_score
            
            # Train LSTM (if TensorFlow is available)
            if TENSORFLOW_AVAILABLE:
                logger.info("Training LSTM model...")
                lstm_score = self._train_lstm(X_train_minmax, y_reg_train, X_test_minmax, y_reg_test)
                performance['lstm'] = lstm_score
            
            # Train Linear Model (simple baseline)
            from sklearn.linear_model import LinearRegression
            linear_model = LinearRegression()
            linear_model.fit(X_train_scaled, y_reg_train)
            linear_pred = linear_model.predict(X_test_scaled)
            linear_score = 1 - mean_squared_error(y_reg_test, linear_pred)
            self.models['linear'] = linear_model
            performance['linear'] = linear_score
            
            # Update model weights based on performance
            self._update_model_weights(performance)
            
            self.is_trained = True
            logger.info("All models trained successfully")
            logger.info(f"Model performance: {performance}")
            
            return performance
            
        except Exception as e:
            logger.error(f"Error training models: {e}")
            return {}
    
    def predict(self, df: pd.DataFrame) -> EnsemblePrediction:
        """
        Make ensemble prediction
        
        Args:
            df: DataFrame with current market data
            
        Returns:
            EnsemblePrediction object
        """
        try:
            if not self.is_trained:
                logger.error("Models not trained yet")
                return None
            
            # Prepare features
            df_features = self.prepare_features(df)
            latest_features = df_features[self.feature_columns].iloc[-1:].fillna(0)
            
            individual_predictions = []
            weighted_sum = 0
            total_weight = 0
            
            # Get predictions from each model
            for model_name, model in self.models.items():
                try:
                    if model_name == 'lstm' and TENSORFLOW_AVAILABLE:
                        # LSTM requires different input format
                        features_scaled = self.scalers['minmax'].transform(latest_features)
                        features_reshaped = features_scaled.reshape((1, 1, features_scaled.shape[1]))
                        prediction = model.predict(features_reshaped)[0][0]
                    elif model_name in ['random_forest', 'xgboost']:
                        prediction = model.predict(latest_features)[0]
                    else:  # linear model
                        features_scaled = self.scalers['standard'].transform(latest_features)
                        prediction = model.predict(features_scaled)[0]
                    
                    # Calculate confidence (simplified)
                    confidence = min(abs(prediction) * 10, 1.0)
                    
                    # Determine direction
                    if prediction > 0.01:
                        direction = 'up'
                    elif prediction < -0.01:
                        direction = 'down'
                    else:
                        direction = 'neutral'
                    
                    model_pred = ModelPrediction(
                        model_name=model_name,
                        prediction=prediction,
                        confidence=confidence,
                        direction=direction
                    )
                    
                    individual_predictions.append(model_pred)
                    
                    # Add to weighted sum
                    weight = self.model_weights.get(model_name, 0.25)
                    weighted_sum += prediction * weight
                    total_weight += weight
                    
                except Exception as e:
                    logger.warning(f"Error getting prediction from {model_name}: {e}")
                    continue
            
            # Calculate final prediction
            if total_weight > 0:
                final_prediction = weighted_sum / total_weight
            else:
                final_prediction = 0.0
            
            # Calculate ensemble confidence
            confidence_scores = [pred.confidence for pred in individual_predictions]
            ensemble_confidence = np.mean(confidence_scores) if confidence_scores else 0.0
            
            # Determine final direction
            if final_prediction > 0.01:
                final_direction = 'up'
            elif final_prediction < -0.01:
                final_direction = 'down'
            else:
                final_direction = 'neutral'
            
            result = EnsemblePrediction(
                final_prediction=final_prediction,
                confidence=ensemble_confidence,
                direction=final_direction,
                individual_predictions=individual_predictions,
                model_weights=self.model_weights.copy()
            )
            
            logger.info(f"Ensemble prediction: {final_prediction:.4f} ({final_direction})")
            return result
            
        except Exception as e:
            logger.error(f"Error making ensemble prediction: {e}")
            return None
    
    def _train_lstm(self, X_train: np.ndarray, y_train: np.ndarray, 
                   X_test: np.ndarray, y_test: np.ndarray) -> float:
        """Train LSTM model"""
        try:
            # Reshape data for LSTM (samples, timesteps, features)
            X_train_lstm = X_train.reshape((X_train.shape[0], 1, X_train.shape[1]))
            X_test_lstm = X_test.reshape((X_test.shape[0], 1, X_test.shape[1]))
            
            # Build LSTM model
            model = Sequential([
                LSTM(50, return_sequences=True, input_shape=(1, X_train.shape[1])),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
            
            # Train model
            model.fit(X_train_lstm, y_train, epochs=50, batch_size=32, 
                     validation_split=0.1, verbose=0)
            
            # Evaluate
            lstm_pred = model.predict(X_test_lstm)
            lstm_score = 1 - mean_squared_error(y_test, lstm_pred.flatten())
            
            self.models['lstm'] = model
            return lstm_score
            
        except Exception as e:
            logger.error(f"Error training LSTM: {e}")
            return 0.0
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add basic technical indicators if not present"""
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        return df
    
    def _update_model_weights(self, performance: Dict[str, float]):
        """Update model weights based on performance"""
        try:
            total_performance = sum(max(score, 0) for score in performance.values())
            
            if total_performance > 0:
                for model_name, score in performance.items():
                    normalized_score = max(score, 0) / total_performance
                    self.model_weights[model_name] = normalized_score
                
                logger.info(f"Updated model weights: {self.model_weights}")
            
        except Exception as e:
            logger.error(f"Error updating model weights: {e}")
    
    def save_models(self, filepath: str):
        """Save trained models"""
        try:
            model_data = {
                'models': {},
                'scalers': self.scalers,
                'model_weights': self.model_weights,
                'feature_columns': self.feature_columns,
                'is_trained': self.is_trained
            }
            
            # Save non-LSTM models
            for name, model in self.models.items():
                if name != 'lstm':
                    model_data['models'][name] = model
            
            joblib.dump(model_data, f"{filepath}_ensemble.pkl")
            
            # Save LSTM separately if it exists
            if 'lstm' in self.models and TENSORFLOW_AVAILABLE:
                self.models['lstm'].save(f"{filepath}_lstm.h5")
            
            logger.info(f"Models saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    def load_models(self, filepath: str):
        """Load trained models"""
        try:
            # Load main models
            model_data = joblib.load(f"{filepath}_ensemble.pkl")
            
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.model_weights = model_data['model_weights']
            self.feature_columns = model_data['feature_columns']
            self.is_trained = model_data['is_trained']
            
            # Load LSTM if it exists
            try:
                if TENSORFLOW_AVAILABLE:
                    self.models['lstm'] = tf.keras.models.load_model(f"{filepath}_lstm.h5")
            except:
                logger.warning("Could not load LSTM model")
            
            logger.info(f"Models loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")

# Example usage
if __name__ == "__main__":
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=1000, freq='4H')
    np.random.seed(42)
    
    # Generate sample OHLCV data
    close_prices = 50000 + np.cumsum(np.random.randn(1000) * 100)
    sample_data = pd.DataFrame({
        'timestamp': dates,
        'open': close_prices + np.random.randn(1000) * 50,
        'high': close_prices + np.abs(np.random.randn(1000) * 100),
        'low': close_prices - np.abs(np.random.randn(1000) * 100),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 1000)
    })
    
    # Initialize and train ensemble
    ensemble = MLEnsemble()
    
    # Prepare features
    df_with_features = ensemble.prepare_features(sample_data)
    
    # Train models
    performance = ensemble.train_models(df_with_features)
    print("Model Performance:", performance)
    
    # Make prediction
    prediction = ensemble.predict(sample_data.tail(100))
    if prediction:
        print(f"Ensemble Prediction: {prediction.final_prediction:.4f}")
        print(f"Direction: {prediction.direction}")
        print(f"Confidence: {prediction.confidence:.2f}")
